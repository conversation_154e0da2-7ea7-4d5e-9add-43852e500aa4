// Package core WebSocket连接核心抽象
package core

import (
	"context"
	"time"
)

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateError
)

// Message WebSocket消息
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	URL               string            `json:"url"`
	ReconnectInterval time.Duration     `json:"reconnect_interval"`
	MaxReconnectTries int               `json:"max_reconnect_tries"`
	PingInterval      time.Duration     `json:"ping_interval"`
	ReadTimeout       time.Duration     `json:"read_timeout"`
	WriteTimeout      time.Duration     `json:"write_timeout"`
	Headers           map[string]string `json:"headers"`
}

// ConnectionStats 连接统计
type ConnectionStats struct {
	ConnectedAt      time.Time `json:"connected_at"`
	ReconnectCount   int       `json:"reconnect_count"`
	MessagesSent     int64     `json:"messages_sent"`
	MessagesReceived int64     `json:"messages_received"`
	LastPingTime     time.Time `json:"last_ping_time"`
	LastPongTime     time.Time `json:"last_pong_time"`
}

// Connection WebSocket连接接口
type Connection interface {
	// Connect 建立连接
	Connect(ctx context.Context) error

	// Disconnect 断开连接
	Disconnect() error

	// Send 发送消息
	Send(ctx context.Context, data []byte) error

	// Receive 接收消息通道
	Receive() <-chan []byte

	// Errors 错误通道
	Errors() <-chan error

	// State 获取连接状态
	State() ConnectionState

	// Stats 获取连接统计
	Stats() ConnectionStats

	// Ping 发送心跳
	Ping(ctx context.Context) error
}

// ConnectionManager 连接管理器接口
type ConnectionManager interface {
	// CreateConnection 创建连接
	CreateConnection(config ConnectionConfig) Connection

	// GetConnection 获取连接
	GetConnection(id string) (Connection, bool)

	// RemoveConnection 移除连接
	RemoveConnection(id string) error

	// GetAllConnections 获取所有连接
	GetAllConnections() map[string]Connection

	// HealthCheck 健康检查
	HealthCheck() map[string]bool
}

// MessageHandler 消息处理器
type MessageHandler interface {
	// Handle 处理消息
	Handle(ctx context.Context, message Message) error

	// CanHandle 检查是否能处理该消息
	CanHandle(message Message) bool
}

// MessageRouter 消息路由器
type MessageRouter interface {
	// RegisterHandler 注册消息处理器
	RegisterHandler(messageType string, handler MessageHandler)

	// UnregisterHandler 取消注册消息处理器
	UnregisterHandler(messageType string)

	// Route 路由消息
	Route(ctx context.Context, message Message) error
}
