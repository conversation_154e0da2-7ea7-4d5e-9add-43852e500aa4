// Package core 定义系统核心抽象
package core

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/os/gtime"
)

const DirectionUp = "up"
const DirectionDown = "down"

// PriceData 价格数据
type PriceData struct {
	Symbol       string      `json:"symbol"`       // 交易对
	Price        float64     `json:"price"`        // 价格
	Timestamp    *gtime.Time `json:"timestamp"`    // K线时间
	MsgTimestamp *gtime.Time `json:"msgTimestamp"` // 交易所消息发送时间
	RxTimestamp  *gtime.Time `json:"rxTimestamp"`  // 本地接收时间
	Source       string      `json:"source"`       // 交易所来源
}

// ArbitrageOpportunity 套利机会
type ArbitrageOpportunity struct {
	Direction        string      `json:"direction"`        // up买入，down卖出
	TriggerTimestamp *gtime.Time `json:"triggerTimestamp"` // 时间戳
	TriggerPriceData *PriceData  `json:"triggerPriceData"` // 触发条件的价格数据
}

// PriceDelay 价格延迟信息
type PriceDelay struct {
	Delay           time.Duration         `json:"delay"`           // 延迟
	Opportunity     *ArbitrageOpportunity `json:"opportunity"`     // 交易机会
	FollowPriceData *PriceData            `json:"followPriceData"` // 跟随交易所价格数据
	DetectTimestamp *gtime.Time           `json:"detectTimestamp"` // 时间戳
}

// PriceStream 价格数据流接口
type PriceStream interface {
	// Subscribe 订阅价格数据
	Subscribe(ctx context.Context, symbol string) (<-chan PriceData, error)

	// Unsubscribe 取消订阅
	Unsubscribe(symbol string) error

	// Close 关闭价格流
	Close() error
}

// Exchange 交易所接口
type Exchange interface {
	// Name 返回交易所名称
	Name() string

	Subscribe(ctx context.Context, symbol string) error

	Unsubscribe(_ context.Context, symbol string) error

	DisconnectAll(ctx context.Context) error
}

// ExchangeManager 交易所管理器接口
type ExchangeManager interface {
	// RegisterExchange 注册交易所
	RegisterExchange(exchange Exchange) error

	// GetExchange 获取交易所
	GetExchange(name string) (Exchange, error)

	// GetAllExchanges 获取所有交易所
	GetAllExchanges() []Exchange

	// DisconnectAll 断开所有交易所连接
	DisconnectAll(ctx context.Context) error
}

// PriceAnalyzer 价格分析器接口
type PriceAnalyzer interface {
	// CheckJumpAndMatch 计算价格跳变和价格延迟
	CheckJumpAndMatch(ctx context.Context, prevPrice, nowPrice float64) (bool, string)
	AnalyzePriceDelay(ctx context.Context, followPrices []PriceData, newTick ArbitrageOpportunity) (
		time.Duration, PriceData,
	)
}

// EventType 事件类型
type EventType string

const (
	EventTypePriceUpdate     EventType = "price_update"
	EventTypeArbitrage       EventType = "arbitrage"
	EventTypeConnectionError EventType = "connection_error"
	EventTypePriceDelay      EventType = "price_delay"
)

// Event 系统事件
type Event struct {
	Type      EventType   `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	Source    string      `json:"source"`
}

// EventBus 事件总线接口
type EventBus interface {
	Publish(event Event)
	Subscribe(eventType EventType, handler func(Event)) (cancel func())
}
