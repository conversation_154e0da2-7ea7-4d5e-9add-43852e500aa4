package cmd

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/gogf/gf/v2/os/gtime"

	"arbitrage/internal/application"
	"arbitrage/internal/core"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gproc"
)

var service *application.ArbitrageService

var Monitor = gcmd.Command{
	Name:  "monitor",
	Usage: "monitor",
	Brief: "start monitor server",
	Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
		service = application.NewArbitrageService(ctx)
		// 初始化服务
		if err := service.Initialize(ctx); err != nil {
			g.Log().Fatalf(ctx, "初始化服务失败: %v", err)
		}

		// 设置事件监听器
		setupEventListeners(ctx, service.GetEventBus())

		// 启动服务
		g.Log().Infof(ctx, "启动套利监控系统...")
		if err := service.Start(); err != nil {
			g.Log().Fatalf(ctx, "启动服务失败: %v", err)
		}

		g.Log().Infof(ctx, "按 Ctrl+C 退出...")

		// Register shutdown handler.
		gproc.AddSigHandlerShutdown(
			func(sig os.Signal) {
				// 优雅关闭
				g.Log().Infof(ctx, "正在关闭系统...")

				ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
				defer cancel()

				done := make(chan error, 1)
				go func() {
					done <- service.Stop(ctx)
				}()

				select {
				case err := <-done:
					if err != nil {
						g.Log().Infof(ctx, "关闭服务时出错: %v", err)
					} else {
						g.Log().Infof(ctx, "系统已安全关闭")
					}
				case <-ctx.Done():
					g.Log().Infof(ctx, "关闭超时，强制退出")
				}

				g.Log().Info(ctx, `price monitor shutdown`)
			},
		)

		g.Listen()

		return nil
	},
}

// setupEventListeners 设置事件监听器
func setupEventListeners(ctx context.Context, eventBus core.EventBus) {
	path := fmt.Sprintf("./data/%s", gtime.Now().Format("Y-m-d"))
	if !gfile.Exists(path) {
		_ = gfile.Mkdir(path)
	} else {
		_ = gfile.RemoveAll(path)
		_ = gfile.Mkdir(path)
	}

	// 监听价格更新事件
	eventBus.Subscribe(
		core.EventTypePriceUpdate, func(event core.Event) {
			priceData, ok := event.Data.(core.PriceData)
			if !ok {
				return
			}

			filePath := fmt.Sprintf("%s/price_%s_%s.csv", path, priceData.Symbol, priceData.Source)
			if !gfile.Exists(filePath) {
				_ = gfile.PutContents(filePath, "价格,时间,K线时间,接收时间\n")
			}
			_ = gfile.PutContentsAppend(
				filePath, fmt.Sprintf(
					"%.4f,%s,%s,%s\n", priceData.Price,
					priceData.Timestamp.Format("Y-m-d H:i:s.u"),
					priceData.MsgTimestamp.Format("Y-m-d H:i:s.u"), priceData.RxTimestamp.Format("Y-m-d H:i:s.u"),
				),
			)
		},
	)

	// 监听套利机会事件
	eventBus.Subscribe(
		core.EventTypeArbitrage, func(event core.Event) {
			opportunity, ok := event.Data.(core.ArbitrageOpportunity)
			if !ok {
				return
			}

			filePath := fmt.Sprintf("%s/order_%s.csv", path, opportunity.TriggerPriceData.Symbol)
			if !gfile.Exists(filePath) {
				_ = gfile.PutContents(filePath, "下单价格,下单时间,下单方向,交易所\n")
			}

			_ = gfile.PutContentsAppend(
				filePath, fmt.Sprintf(
					"%.4f,%s,%s,%s\n", opportunity.TriggerPriceData.Price,
					opportunity.TriggerTimestamp.Format("Y-m-d H:i:s.u"),
					opportunity.Direction, opportunity.TriggerPriceData.Source,
				),
			)
		},
	)

	// 处理价格延迟事件
	eventBus.Subscribe(
		core.EventTypePriceDelay, func(event core.Event) {
			delay, ok := event.Data.(core.PriceDelay)
			if !ok {
				return
			}

			filePath := fmt.Sprintf("%s/delay_%s.csv", path, delay.Opportunity.TriggerPriceData.Symbol)
			if !gfile.Exists(filePath) {
				_ = gfile.PutContents(
					filePath, "主交易所价格,主交易所,主交易所时间,从交易所价格,从交易所,从交易所时间,延迟,检测时间\n",
				)
			}
			_ = gfile.PutContentsAppend(
				filePath, fmt.Sprintf(
					"%.4f,%s,%s,%.4f,%s,%s,%dms,%s\n",
					delay.Opportunity.TriggerPriceData.Price, delay.Opportunity.TriggerPriceData.Source,
					delay.Opportunity.TriggerPriceData.MsgTimestamp.Format("Y-m-d H:i:s.u"),
					delay.FollowPriceData.Price, delay.FollowPriceData.Source,
					delay.FollowPriceData.MsgTimestamp.Format("Y-m-d H:i:s.u"),
					delay.Delay.Milliseconds(),
					delay.DetectTimestamp.Format("Y-m-d H:i:s.u"),
				),
			)
		},
	)
}
