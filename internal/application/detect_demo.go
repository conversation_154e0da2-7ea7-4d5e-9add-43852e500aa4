package application

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"os"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"

	"arbitrage/internal/core"
	"arbitrage/internal/infrastructure/analyzer"
	"arbitrage/internal/infrastructure/event"
	"arbitrage/internal/infrastructure/exchange"
)

func Demo() {
	ctx := gctx.GetInitCtx()
	cancelCtx, cancel := context.WithCancel(ctx)

	eventBus := event.NewEventBus()
	as := &ArbitrageService{
		exchangeManager:   exchange.NewManager(),
		eventBus:          eventBus,
		priceCache:        make(map[string]map[string][]core.PriceData),
		priceDelays:       make(map[string]map[string]core.PriceDelay),
		symbolAnalyzerMap: make(map[string]core.PriceAnalyzer),
		ctx:               cancelCtx,
		cancel:            cancel,
	}

	as.SetupEventHandlers(ctx)

	as.eventBus.Subscribe(
		core.EventTypePriceDelay, func(event core.Event) {
			priceDelay, ok := event.Data.(core.PriceDelay)
			if !ok {
				return
			}

			g.Log().Infof(
				ctx,
				"延迟: %d ms, 时间: %s, 主交易所: %s, 跟随交易所: %s, 主交易所价格: %f, 跟随交易所价格: %f, 主交易所时间: %s, 跟随交易所时间: %s",
				priceDelay.Delay.Milliseconds(), priceDelay.DetectTimestamp.Format("Y-m-d H:i:s.u"),
				priceDelay.Opportunity.TriggerPriceData.Source, priceDelay.FollowPriceData.Source,
				priceDelay.Opportunity.TriggerPriceData.Price, priceDelay.FollowPriceData.Price,
				priceDelay.Opportunity.TriggerPriceData.MsgTimestamp.Format("Y-m-d H:i:s.u"),
				priceDelay.FollowPriceData.MsgTimestamp.Format("Y-m-d H:i:s.u"),
			)
		},
	)

	symbol := "LDO"
	as.symbolAnalyzerMap[symbol] = analyzer.NewPriceAnalyzer(0.001, 0.0005)

	wg := &sync.WaitGroup{}
	loadData(as, symbol, eventBus, wg)

	wg.Wait()
}

func streamCSV(path, exchange, symbol string, out chan<- core.PriceData) {
	f, err := os.Open(path)
	if err != nil {
		log.Fatalf("无法打开文件 %s: %v", path, err)
	}
	defer f.Close()

	reader := csv.NewReader(f)
	reader.FieldsPerRecord = -1
	_, _ = reader.Read() // 跳过表头

	for {
		record, err := reader.Read()
		if err == io.EOF {
			close(out)
			return
		}
		if err != nil || len(record) < 3 {
			continue
		}

		price := gconv.Float64(record[0])
		klineTime := gtime.New(record[1])

		out <- core.PriceData{
			Symbol:       symbol,
			Price:        price,
			Timestamp:    klineTime,
			MsgTimestamp: gtime.New(record[2]),
			RxTimestamp:  gtime.New(record[3]),
			Source:       exchange,
		}
	}
}

func loadData(as *ArbitrageService, symbol string, event core.EventBus, wg *sync.WaitGroup) {
	lbankCh := make(chan core.PriceData)
	mexcCh := make(chan core.PriceData)

	lbankPath := fmt.Sprintf("/Users/<USER>/Project/go/arbitrage/data/2025-06-18/price_%s_lbank.csv", symbol)
	mexcPath := fmt.Sprintf("/Users/<USER>/Project/go/arbitrage/data/2025-06-18/price_%s_mexc.csv", symbol)

	go func() {
		wg.Add(1)
		streamCSV(lbankPath, "lbank", symbol, lbankCh)
		wg.Done()
	}()
	go func() {
		wg.Add(1)
		streamCSV(mexcPath, "mexc", symbol, mexcCh)
		wg.Done()
	}()

	var lbank, mexc *core.PriceData

	for lbankCh != nil || mexcCh != nil {
		if lbank == nil && lbankCh != nil {
			if d, ok := <-lbankCh; ok {
				lbank = &d
			} else {
				lbankCh = nil
			}
		}

		if mexc == nil && mexcCh != nil {
			if d, ok := <-mexcCh; ok {
				mexc = &d
			} else {
				mexcCh = nil
			}
		}

		ctx := context.TODO()

		if lbank != nil && (mexc == nil || lbank.MsgTimestamp.Before(mexc.MsgTimestamp)) {
			as.updatePriceCache(ctx, *lbank)

			event.Publish(
				core.Event{
					Type:      core.EventTypePriceUpdate,
					Data:      *lbank,
					Timestamp: lbank.RxTimestamp.Time,
					Source:    "lbank",
				},
			)
			// fmt.Printf(
			// 	"价格: %f, 时间: %s, 交易所: %s \n", lbank.Price, lbank.MsgTimestamp.Format("Y-m-d H:i:s.u"),
			// 	lbank.Source,
			// )

			lbank = nil
		} else if mexc != nil {
			as.updatePriceCache(ctx, *mexc)

			event.Publish(
				core.Event{
					Type:      core.EventTypePriceUpdate,
					Data:      *mexc,
					Timestamp: mexc.RxTimestamp.Time,
					Source:    "mexc",
				},
			)
			// fmt.Printf(
			// 	"价格: %f, 时间: %s, 交易所: %s \n", mexc.Price, mexc.MsgTimestamp.Format("Y-m-d H:i:s.u"), mexc.Source,
			// )

			mexc = nil
		}
		// 模拟数据真实接收，不延时价格推送太快了
		// clearExpiredPriceData 里面的逻辑
		// 前面的价格还没检测，后面的价格已经把前面的价格刷掉了
		time.Sleep(time.Millisecond * 5)
	}
}
