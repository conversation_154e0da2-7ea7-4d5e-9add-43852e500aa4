// Package application 套利服务应用层
package application

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	"arbitrage/internal/core"
	"arbitrage/internal/infrastructure/analyzer"
	"arbitrage/internal/infrastructure/event"
	"arbitrage/internal/infrastructure/exchange"

	"github.com/gogf/gf/v2/os/gtime"
)

const (
	DelayThreshold = 200 * time.Millisecond // 允许延迟范围
	PriceExpired   = 10 * time.Second
)

// ArbitrageService 套利服务
type ArbitrageService struct {
	exchangeManager core.ExchangeManager
	eventBus        core.EventBus

	// 价格数据缓存
	priceCache map[string]map[string][]core.PriceData // [symbol][exchange][]PriceData
	cacheMu    sync.RWMutex

	monitorConfig []*MonitorConfig

	// 价格延迟记录
	priceDelays map[string]map[string]core.PriceDelay // [symbol][exchange_pair]PriceDelay
	delayMu     sync.RWMutex

	// 每个symbol的priceAnalyzer
	symbolAnalyzerMap map[string]core.PriceAnalyzer

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

type MonitorConfig struct {
	Symbol             string  `json:"symbol"`
	PriceJumpThreshold float64 `json:"priceJumpThreshold"`
	PriceEpsilon       float64 `json:"priceEpsilon"`
	DelayThreshold     float64 `json:"delayThreshold"`
}

// NewArbitrageService 创建套利服务
func NewArbitrageService(ctx context.Context) *ArbitrageService {
	cancelCtx, cancel := context.WithCancel(ctx)

	var monitorConfig []*MonitorConfig
	cfgValue, _ := g.Cfg().Get(ctx, "monitor")
	if err := cfgValue.Scan(&monitorConfig); err != nil {
		g.Log().Fatalf(ctx, "读取配置失败: %v", err)
	} else if len(monitorConfig) == 0 {
		g.Log().Fatalf(ctx, "无效的监控配置")
	}

	symbolAnalyzerMap := make(map[string]core.PriceAnalyzer)
	for _, cfg := range monitorConfig {
		symbolAnalyzerMap[cfg.Symbol] = analyzer.NewPriceAnalyzer(cfg.PriceJumpThreshold, cfg.PriceEpsilon)
	}

	return &ArbitrageService{
		exchangeManager:   exchange.NewManager(),
		eventBus:          event.NewEventBus(),
		priceCache:        make(map[string]map[string][]core.PriceData),
		priceDelays:       make(map[string]map[string]core.PriceDelay),
		monitorConfig:     monitorConfig,
		symbolAnalyzerMap: symbolAnalyzerMap,
		ctx:               cancelCtx,
		cancel:            cancel,
	}
}

// Initialize 初始化服务
func (as *ArbitrageService) Initialize(ctx context.Context) error {
	// 注册交易所
	mexcExchange := exchange.NewMexcExchange(as.eventBus)
	lbankExchange := exchange.NewLBankExchange(as.eventBus)

	if err := as.exchangeManager.RegisterExchange(mexcExchange); err != nil {
		return fmt.Errorf("注册MEXC交易所失败: %w", err)
	}

	if err := as.exchangeManager.RegisterExchange(lbankExchange); err != nil {
		return fmt.Errorf("注册LBank交易所失败: %w", err)
	}

	// 订阅事件
	as.SetupEventHandlers(ctx)

	return nil
}

// Start 启动服务
func (as *ArbitrageService) Start() error {
	if len(as.monitorConfig) == 0 {
		return gerror.New("未配置监控交易对")
	}

	// 为每个符号启动价格监控
	for _, cfg := range as.monitorConfig {
		as.wg.Add(1)
		go as.monitorSymbolWithReconnect(cfg.Symbol)

		g.Log().Infof(as.ctx, "正在监控交易对: %v", cfg.Symbol)
	}

	// 订阅连接错误事件，自动重连并重订阅
	as.eventBus.Subscribe(
		core.EventTypeConnectionError, func(event core.Event) {
			exName := event.Source
			var symbols []string
			if err := gconv.Scan(event.Data, &symbols); err != nil {
				g.Log().Errorf(as.ctx, "连接错误事件数据解析失败: %v", err)
				return
			}

			g.Log().Warningf(as.ctx, "检测到交易所 %s 连接断开，尝试重连...", exName)
			as.handleExchangeReconnect(exName, symbols)
		},
	)

	return nil
}

// Stop 停止服务
func (as *ArbitrageService) Stop(ctx context.Context) error {
	as.cancel()
	if err := as.exchangeManager.DisconnectAll(ctx); err != nil {
		return fmt.Errorf("断开交易所连接失败: %w", err)
	}
	as.wg.Wait()
	return nil
}

// GetEventBus 获取事件总线
func (as *ArbitrageService) GetEventBus() core.EventBus {
	return as.eventBus
}

// monitorSymbolWithReconnect 监控特定symbol，支持断线重连和自动重订阅
func (as *ArbitrageService) monitorSymbolWithReconnect(symbol string) {
	defer as.wg.Done()

	exchanges := as.exchangeManager.GetAllExchanges()
	// 维护每个交易所的订阅通道
	priceChans := make(map[string]<-chan core.PriceData)

	subscribeAll := func() {
		for _, ex := range exchanges {
			err := ex.Subscribe(as.ctx, symbol)
			if err != nil {
				g.Log().Warningf(as.ctx, "订阅 %s 在 %s 的价格失败: %v", symbol, ex.Name(), err)
				continue
			}
		}
	}

	subscribeAll()

	for {
		select {
		case <-as.ctx.Done():
			return
		default:
			for exName, ch := range priceChans {
				select {
				case priceData, ok := <-ch:
					if !ok {
						// 通道关闭，尝试重订阅
						g.Log().Warningf(as.ctx, "%s 的订阅通道关闭，尝试重订阅...", exName)
						delete(priceChans, exName)
						continue
					}
					as.updatePriceCache(as.ctx, priceData)
					as.eventBus.Publish(
						core.Event{
							Type:      core.EventTypePriceUpdate,
							Data:      priceData,
							Timestamp: time.Now(),
							Source:    exName,
						},
					)
				default:
					// 非阻塞
				}
			}
			time.Sleep(10 * time.Millisecond)
		}
	}
}

// handleExchangeReconnect 处理交易所断线重连和重订阅
func (as *ArbitrageService) handleExchangeReconnect(exName string, symbols []string) {
	ex, err := as.exchangeManager.GetExchange(exName)
	if err != nil {
		g.Log().Errorf(as.ctx, "获取交易所 %s 失败: %v", exName, err)
		return
	}
	g.Log().Infof(as.ctx, "交易所 %s 重连成功，重新订阅交易对 %s", exName, symbols)

	symbolMap := make(map[string]bool)
	for _, val := range symbols {
		symbolMap[val] = true
	}

	// 重新订阅所有监控symbol
	for _, cfg := range as.monitorConfig {
		if _, ok := symbolMap[cfg.Symbol]; ok {
			if err = ex.Subscribe(as.ctx, cfg.Symbol); err != nil {
				g.Log().Warningf(as.ctx, "重订阅 %s 在 %s 失败: %v", cfg.Symbol, exName, err)
			}
		}
	}
}

// DetectArbitrage 检测套利机会
func (as *ArbitrageService) DetectArbitrage(ctx context.Context, triggerPriceData core.PriceData) {
	as.cacheMu.RLock()
	defer as.cacheMu.RUnlock()

	symbol := triggerPriceData.Symbol
	source := triggerPriceData.Source
	exchangePrices, ok := as.priceCache[symbol]
	if !ok {
		return
	}

	if len(exchangePrices[source]) == 0 {
		return
	}

	priceAnalyzer, ok := as.symbolAnalyzerMap[symbol]
	if !ok {
		return
	}

	priceData := exchangePrices[source]

	if len(priceData) < 2 {
		return
	}

	prevPriceData := priceData[len(priceData)-2]

	isOpportunity, direction := priceAnalyzer.CheckJumpAndMatch(ctx, prevPriceData.Price, triggerPriceData.Price)
	if !isOpportunity {
		return
	}

	// changeRate := math.Abs(triggerPriceData.Price-prevPrice) / prevPrice

	// fmt.Printf(
	// 	"检测到跳变, 交易所: %s 时间: %s, 前一个价格: %f 现在价格: %f 波动: %f 方向: %s \n", source,
	// 	triggerPriceData.MsgTimestamp.Format("Y-m-d H:i:s.u"),
	// 	prevPrice,
	// 	triggerPriceData.Price,
	// 	changeRate,
	// 	direction,
	// )

	opportunity := core.ArbitrageOpportunity{
		Direction:        direction,
		TriggerTimestamp: gtime.Now(),
		TriggerPriceData: &triggerPriceData,
	}

	// 发布套利机会事件
	as.eventBus.Publish(
		core.Event{
			Type:      core.EventTypeArbitrage,
			Data:      opportunity,
			Timestamp: time.Now(),
			Source:    fmt.Sprintf("arbitrage_detector_%s", symbol),
		},
	)
}

// DetectPriceDelay 检测价格延迟
func (as *ArbitrageService) DetectPriceDelay(ctx context.Context, opportunity core.ArbitrageOpportunity) {
	as.cacheMu.RLock()
	symbol := opportunity.TriggerPriceData.Symbol
	exchangePrices, ok := as.priceCache[symbol]
	if !ok {
		g.Log().Warningf(ctx, "没找到 %s 的价格数据", symbol)
		return
	}
	as.cacheMu.RUnlock()

	var exchangeList []string
	for ex := range exchangePrices {
		exchangeList = append(exchangeList, ex)
	}

	var detectDone chan bool

	var cancelFunc func()
	go func() {
		cancelFunc = as.eventBus.Subscribe(
			core.EventTypePriceUpdate, func(event core.Event) {
				priceData, ok := event.Data.(core.PriceData)
				if !ok {
					return
				}

				if len(exchangeList) == 0 {
					// g.Log().Warningf(ctx, "交易所都检查完了")

					detectDone <- true

					return
				}

				// 价格有更新，检测价格延迟
				if priceData.Source != opportunity.TriggerPriceData.Source {
					exchangeList = as.detectPriceDelay(ctx, exchangeList, opportunity)

				}
			},
		)

	}()

	for {
		select {
		case <-as.ctx.Done():
			if cancelFunc != nil {
				cancelFunc()
				cancelFunc = nil

			}

			g.Log().Warningf(ctx, "没有检测到价格延迟")

			return
		case <-detectDone:
			g.Log().Warningf(ctx, "检查完了，没有检测到价格延迟")
		case <-time.After(5 * time.Second):
			if cancelFunc != nil {
				cancelFunc()
				cancelFunc = nil
			}
			g.Log().Warningf(ctx, "检测超时, 没有检测到")

			return
		}
	}
}

func (as *ArbitrageService) detectPriceDelay(
	ctx context.Context, exchangeList []string, opportunity core.ArbitrageOpportunity,
) []string {
	as.cacheMu.RLock()
	symbol := opportunity.TriggerPriceData.Symbol
	exchangePrices, ok := as.priceCache[symbol]
	if !ok {
		return exchangeList
	}

	as.cacheMu.RUnlock()

	if opportunity.TriggerPriceData == nil {
		g.Log().Warningf(
			ctx, "触发价格数据为空: 方向:%s 主导交易所:%s 跟随交易所:%s 触发时间: %s", opportunity.Direction,
			opportunity.TriggerPriceData.Source,
			opportunity.TriggerTimestamp.Format("Y-m-d H:i:s.u"),
		)
		return nil
	}

	priceAnalyzer, ok := as.symbolAnalyzerMap[symbol]
	if !ok {
		return nil
	}

	var waitDetectExchange []string
	for _, ex := range exchangeList {
		priceData := exchangePrices[ex]

		if ex == opportunity.TriggerPriceData.Source {
			continue
		}
		delay, findPriceData := priceAnalyzer.AnalyzePriceDelay(ctx, priceData, opportunity)

		if delay == 0 {
			// g.Log().Warningf(
			// 	ctx, "价格没有匹配，等待下一个价格数据,最新价格: %f, 触发价格: %f", priceData[len(priceData)-1].Price,
			// 	opportunity.TriggerPriceData.Price,
			// )
			waitDetectExchange = append(waitDetectExchange, ex)
		}

		if delay <= DelayThreshold {
			// g.Log().Warningf(ctx, "延迟时间小于阈值：%d ms", DelayThreshold.Milliseconds())
			continue
		}

		delayKey := ex
		as.delayMu.Lock()
		if as.priceDelays[symbol] == nil {
			as.priceDelays[symbol] = make(map[string]core.PriceDelay)
		}

		priceDelayData := core.PriceDelay{
			Delay:           delay,
			Opportunity:     &opportunity,
			FollowPriceData: &findPriceData,
			DetectTimestamp: gtime.Now(),
		}
		as.priceDelays[symbol][delayKey] = priceDelayData
		as.delayMu.Unlock()

		// 发布价格延迟事件
		as.eventBus.Publish(
			core.Event{
				Type:      core.EventTypePriceDelay,
				Data:      priceDelayData,
				Timestamp: time.Now(),
				Source:    "price_delay_detector",
			},
		)
	}

	return waitDetectExchange
}

// updatePriceCache 更新价格缓存
func (as *ArbitrageService) updatePriceCache(ctx context.Context, priceData core.PriceData) {
	as.cacheMu.Lock()
	defer as.cacheMu.Unlock()

	if as.priceCache[priceData.Symbol] == nil {
		as.priceCache[priceData.Symbol] = make(map[string][]core.PriceData)
	}

	if _, ok := as.priceCache[priceData.Symbol][priceData.Source]; !ok {
		as.priceCache[priceData.Symbol][priceData.Source] = make([]core.PriceData, 0, 10)
	}
	as.priceCache[priceData.Symbol][priceData.Source] = append(
		as.priceCache[priceData.Symbol][priceData.Source], priceData,
	)

	as.priceCache[priceData.Symbol][priceData.Source] = as.clearExpiredPriceData(as.priceCache[priceData.Symbol][priceData.Source])
}

// clearExpiredPriceData 清除过期的价格数据
func (as *ArbitrageService) clearExpiredPriceData(priceDataList []core.PriceData) (validData []core.PriceData) {
	// 按接收时间排序
	sort.Slice(
		priceDataList, func(i, j int) bool {
			return priceDataList[i].RxTimestamp.Before(priceDataList[j].RxTimestamp)
		},
	)

	if len(priceDataList) > 2 {
		newTicker := priceDataList[len(priceDataList)-1]

		for _, priceData := range priceDataList {
			if priceData.RxTimestamp.After(newTicker.RxTimestamp.Add(-PriceExpired)) {
				validData = append(validData, priceData)
			}
		}
	} else {
		validData = priceDataList
	}

	return
}

// SetupEventHandlers 设置事件处理器
func (as *ArbitrageService) SetupEventHandlers(ctx context.Context) {
	// 处理连接错误事件
	as.eventBus.Subscribe(
		core.EventTypePriceUpdate, func(event core.Event) {
			priceData, ok := event.Data.(core.PriceData)
			if !ok {
				return
			}

			// 检测套利机会
			as.DetectArbitrage(ctx, priceData)
		},
	)

	// 检测到价格跳变后，开始检测价格延迟
	as.eventBus.Subscribe(
		core.EventTypeArbitrage, func(event core.Event) {
			opportunity, ok := event.Data.(core.ArbitrageOpportunity)
			if !ok {
				return
			}

			// 检测价格延迟
			as.DetectPriceDelay(ctx, opportunity)
		},
	)
}
