// Package analyzer 价格分析器实现
package analyzer

import (
	"context"
	"math"
	"time"

	"arbitrage/internal/core"
)

// priceAnalyzer 价格分析器实现
type priceAnalyzer struct {
	jumpThreshold float64
	priceEpsilon  float64
}

// NewPriceAnalyzer 创建新的价格分析器
func NewPriceAnalyzer(jumpThreshold, priceEpsilon float64) core.PriceAnalyzer {
	return &priceAnalyzer{
		jumpThreshold: jumpThreshold,
		priceEpsilon:  priceEpsilon,
	}
}

func (pa *priceAnalyzer) CheckJumpAndMatch(_ context.Context, prevPrice, nowPrice float64) (
	priceJump bool, direction string,
) {
	changeRate := math.Abs(nowPrice-prevPrice) / prevPrice
	if changeRate < pa.jumpThreshold {
		return
	}

	priceJump = true

	direction = core.DirectionUp
	if nowPrice < prevPrice {
		direction = core.DirectionDown
	}

	return
}

func (pa *priceAnalyzer) AnalyzePriceDelay(
	_ context.Context, followPrices []core.PriceData, opportunity core.ArbitrageOpportunity,
) (delay time.Duration, findPriceData core.PriceData) {
	if opportunity.TriggerPriceData == nil {
		return
	}

	triggerPriceData := opportunity.TriggerPriceData

	// 找到第一个晚于触发时间的价格数据
	var findIdx int
	for idx, priceData := range followPrices {
		if priceData.MsgTimestamp.After(triggerPriceData.MsgTimestamp) {
			findIdx = idx

			break
		}
	}

	for _, priceData := range followPrices[findIdx:] {
		if opportunity.Direction == core.DirectionUp {
			if priceData.Price >= triggerPriceData.Price*(1-pa.priceEpsilon) {
				delay = priceData.MsgTimestamp.Sub(triggerPriceData.MsgTimestamp)
				findPriceData = priceData

				// g.Log().Printf(
				// 	context.TODO(), "价格: %f, 时间: %s, 延迟: %d ms, 交易所: %s, 触发交易所: %s", priceData.Price,
				// 	priceData.MsgTimestamp.Format("Y-m-d H:i:s.u"), delay.Milliseconds(),
				// 	priceData.Source, triggerPriceData.Source,
				// )

				return
			}
		} else {
			if priceData.Price <= triggerPriceData.Price*(1+pa.priceEpsilon) {
				delay = priceData.MsgTimestamp.Sub(triggerPriceData.MsgTimestamp)
				findPriceData = priceData

				// g.Log().Printf(
				// 	context.TODO(), "价格: %f, 时间: %s, 延迟: %d ms, 交易所: %s, 触发交易所: %s", priceData.Price,
				// 	priceData.MsgTimestamp.Format("Y-m-d H:i:s.u"), delay.Milliseconds(),
				// 	priceData.Source, triggerPriceData.Source,
				// )

				return
			}
		}
	}

	return

}
