package event

import (
	"context"
	"sync"

	"github.com/gogf/gf/v2/util/guid"

	"arbitrage/internal/core"
)

type subscriber struct {
	ctx     context.Context
	cancel  context.CancelFunc
	handler func(core.Event)
}

type rxEventBus struct {
	mu          sync.RWMutex
	subscribers map[core.EventType]map[string]*subscriber
}

func NewEventBus() core.EventBus {
	return &rxEventBus{
		subscribers: make(map[core.EventType]map[string]*subscriber),
	}
}

func (eb *rxEventBus) Publish(event core.Event) {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	listeners := eb.subscribers[event.Type]
	for _, sub := range listeners {
		go func(s *subscriber) {
			select {
			case <-s.ctx.Done():
				return
			default:
				s.handler(event)
			}
		}(sub)
	}
}

func (eb *rxEventBus) Subscribe(eventType core.EventType, handler func(core.Event)) (cancel func()) {
	ctx, cancelFunc := context.WithCancel(context.Background())

	sub := &subscriber{
		ctx:     ctx,
		cancel:  cancelFunc,
		handler: handler,
	}

	uuid := guid.S()

	eb.mu.Lock()
	if _, ok := eb.subscribers[eventType]; !ok {
		eb.subscribers[eventType] = make(map[string]*subscriber)
	}
	eb.subscribers[eventType][uuid] = sub
	eb.mu.Unlock()

	return func() {
		cancelFunc()

		// 从订阅列表中移除
		eb.mu.Lock()
		defer eb.mu.Unlock()
		subs := eb.subscribers[eventType]
		delete(subs, uuid)
	}
}
