// Package websocket WebSocket连接实现
package websocket

import (
	"context"
	"sync"
	"sync/atomic"
	"time"

	"arbitrage/internal/core"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gorilla/websocket"
)

// connection WebSocket连接实现
type connection struct {
	config  core.ConnectionConfig
	conn    *websocket.Conn
	state   int32 // 使用atomic操作
	stats   core.ConnectionStats
	statsMu sync.RWMutex
	writeMu sync.Mutex // 写入锁，保护并发写入

	// 通道
	messagesCh chan []byte
	errorsCh   chan error
	closeCh    chan struct{}

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewConnection 创建新的WebSocket连接
func NewConnection(config core.ConnectionConfig) core.Connection {
	ctx, cancel := context.WithCancel(context.Background())

	return &connection{
		config:     config,
		state:      int32(core.StateDisconnected),
		messagesCh: make(chan []byte, 100),
		errorsCh:   make(chan error, 10),
		closeCh:    make(chan struct{}),
		ctx:        ctx,
		cancel:     cancel,
	}
}

// Connect 建立连接
func (c *connection) Connect(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&c.state, int32(core.StateDisconnected), int32(core.StateConnecting)) {
		return gerror.Newf("连接状态不正确，当前状态: %v", c.State())
	}

	// 建立WebSocket连接
	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	conn, _, err := dialer.DialContext(ctx, c.config.URL, nil)
	if err != nil {
		atomic.StoreInt32(&c.state, int32(core.StateError))
		return gerror.Wrapf(err, "连接失败")
	}

	c.conn = conn
	atomic.StoreInt32(&c.state, int32(core.StateConnected))

	// 更新统计信息
	c.statsMu.Lock()
	c.stats.ConnectedAt = time.Now()
	c.statsMu.Unlock()

	// 启动读写协程
	c.wg.Add(3)
	go c.readLoop()
	go c.writeLoop()
	go c.pingLoop()

	return nil
}

// Disconnect 断开连接
func (c *connection) Disconnect() error {
	if atomic.LoadInt32(&c.state) == int32(core.StateDisconnected) {
		return nil
	}

	atomic.StoreInt32(&c.state, int32(core.StateDisconnected))

	// 取消上下文
	c.cancel()

	// 关闭WebSocket连接
	if c.conn != nil {
		c.conn.Close()
	}

	// 等待所有协程结束
	c.wg.Wait()

	// 关闭通道
	close(c.closeCh)

	return nil
}

// Send 发送消息
func (c *connection) Send(ctx context.Context, data []byte) error {
	if atomic.LoadInt32(&c.state) != int32(core.StateConnected) {
		return gerror.New("连接未建立")
	}

	// 加锁保护并发写入
	c.writeMu.Lock()
	defer c.writeMu.Unlock()

	_ = c.conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
	err := c.conn.WriteMessage(websocket.TextMessage, data)
	if err != nil {
		return gerror.Wrapf(err, "发送消息失败")
	}

	// 更新统计
	c.statsMu.Lock()
	c.stats.MessagesSent++
	c.statsMu.Unlock()

	return nil
}

// Receive 接收消息通道
func (c *connection) Receive() <-chan []byte {
	return c.messagesCh
}

// Errors 错误通道
func (c *connection) Errors() <-chan error {
	return c.errorsCh
}

// State 获取连接状态
func (c *connection) State() core.ConnectionState {
	return core.ConnectionState(atomic.LoadInt32(&c.state))
}

// Stats 获取连接统计
func (c *connection) Stats() core.ConnectionStats {
	c.statsMu.RLock()
	defer c.statsMu.RUnlock()
	return c.stats
}

// Ping 发送心跳
func (c *connection) Ping(ctx context.Context) error {
	if atomic.LoadInt32(&c.state) != int32(core.StateConnected) {
		return gerror.New("连接未建立")
	}

	// 加锁保护并发写入
	c.writeMu.Lock()
	defer c.writeMu.Unlock()

	_ = c.conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
	err := c.conn.WriteMessage(websocket.PingMessage, []byte{})
	if err != nil {
		return gerror.Wrapf(err, "发送心跳失败")
	}

	// 更新统计
	c.statsMu.Lock()
	c.stats.LastPingTime = time.Now()
	c.statsMu.Unlock()

	return nil
}

// readLoop 读取消息循环
func (c *connection) readLoop() {
	defer c.wg.Done()

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			// 设置读取超时
			_ = c.conn.SetReadDeadline(time.Now().Add(c.config.ReadTimeout))

			messageType, data, err := c.conn.ReadMessage()
			if err != nil {
				if atomic.LoadInt32(&c.state) == int32(core.StateConnected) {
					c.errorsCh <- gerror.Wrapf(err, "读取消息失败")
				}
				return
			}

			// 处理不同类型的消息
			switch messageType {
			case websocket.TextMessage:
				select {
				case c.messagesCh <- data:
					// 更新统计
					c.statsMu.Lock()
					c.stats.MessagesReceived++
					c.statsMu.Unlock()
				case <-c.ctx.Done():
					return
				}

			case websocket.BinaryMessage:
				select {
				case c.messagesCh <- data:
					// 更新统计
					c.statsMu.Lock()
					c.stats.MessagesReceived++
					c.statsMu.Unlock()
				case <-c.ctx.Done():
					return
				}

			case websocket.PongMessage:
				// 更新pong时间
				c.statsMu.Lock()
				c.stats.LastPongTime = time.Now()
				c.statsMu.Unlock()
			}
		}
	}
}

// writeLoop 写入消息循环
func (c *connection) writeLoop() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			// 定期检查连接状态
			if atomic.LoadInt32(&c.state) != int32(core.StateConnected) {
				return
			}
		}
	}
}

// pingLoop 心跳循环
func (c *connection) pingLoop() {
	defer c.wg.Done()

	if c.config.PingInterval <= 0 {
		return
	}

	ticker := time.NewTicker(c.config.PingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			if err := c.Ping(c.ctx); err != nil {
				c.errorsCh <- gerror.Wrapf(err, "心跳失败")
				return
			}
		}
	}
}
