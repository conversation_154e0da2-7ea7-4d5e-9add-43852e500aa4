// Package exchange 交易所处理器
package exchange

import (
	"context"

	"arbitrage/internal/core"
)

// PriceStreamAdapter 定义了价格流的适配器接口
type PriceStreamAdapter interface {
	ToExchangeSymbol(symbol string) string
	FromExchangeSymbol(symbol string) string
	BuildSubscribeMsg(symbol string) ([]byte, error)
	BuildUnsubscribeMsg(symbol string) ([]byte, error)
}

// Handler defines the exchange-specific logic.
type Handler interface {
	Name() string
	ProcessMessage(ctx context.Context, data []byte, conn *SymbolConn)
	BuildHealthCheckMsg() ([]byte, error)
	GetConnectionConfig() core.ConnectionConfig
	GetPriceStreamAdapter() PriceStreamAdapter
}
