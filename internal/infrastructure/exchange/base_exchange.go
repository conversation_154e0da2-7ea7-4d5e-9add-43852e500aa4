// Package exchange 基础交易所实现
package exchange

import (
	"context"
	"sync"
	"time"

	"arbitrage/internal/core"
	"arbitrage/internal/infrastructure/websocket"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// baseExchange provides a foundation for exchange implementations.
type baseExchange struct {
	handler             Handler
	connectionMaxSymbol int
	connPool            []*SymbolConn
	connMu              sync.RWMutex
	eventBus            core.EventBus
}

// SymbolConn maintains the state for a symbol connection.
type SymbolConn struct {
	conn            core.Connection
	symbols         map[string]bool // listening symbols
	symbolMu        sync.RWMutex
	basePriceStream *basePriceStream
	exchange        *baseExchange
}

// BasePriceStream 获取 SymbolConn 的 BasePriceStream
func (sc *SymbolConn) BasePriceStream() *basePriceStream {
	return sc.basePriceStream
}

// NewBaseExchange creates a new base exchange.
func NewBaseExchange(handler <PERSON><PERSON>, eventBus core.EventBus, connectionMaxSymbol int) core.Exchange {
	return &baseExchange{
		handler:             handler,
		eventBus:            eventBus,
		connectionMaxSymbol: connectionMaxSymbol,
		connPool:            []*SymbolConn{},
	}
}

func (e *baseExchange) initConnection() core.Connection {
	config := e.handler.GetConnectionConfig()
	return websocket.NewConnection(config)
}

// Name 返回交易所名称
func (e *baseExchange) Name() string {
	return e.handler.Name()
}

// Subscribe 订阅
func (e *baseExchange) Subscribe(ctx context.Context, symbol string) (err error) {
	e.connMu.Lock()
	var symbolConn *SymbolConn
	for _, val := range e.connPool {
		if len(val.symbols) < e.connectionMaxSymbol {
			symbolConn = val
			break
		}
	}

	if symbolConn == nil {
		cancelCtx, cancel := context.WithCancel(ctx)
		symbolConn = &SymbolConn{
			symbols:  map[string]bool{symbol: true},
			conn:     e.initConnection(),
			exchange: e,
		}

		symbolConn.basePriceStream = &basePriceStream{
			adapter: e.handler.GetPriceStreamAdapter(),
			ctx:     cancelCtx,
			cancel:  cancel,
			sendFunc: func(ctx context.Context, data []byte) error {
				if symbolConn.conn == nil {
					return gerror.Newf("%s connection not established", e.Name())
				}

				return symbolConn.conn.Send(ctx, data)
			},
		}

		e.connPool = append(e.connPool, symbolConn)
	} else {
		symbolConn.symbolMu.Lock()
		symbolConn.symbols[symbol] = true
		symbolConn.symbolMu.Unlock()
	}
	e.connMu.Unlock()

	timeout := time.After(10 * time.Second)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

waitLoop:
	for {
		select {
		case <-timeout:
			e.Disconnect(ctx, symbolConn)
			return gerror.Newf("%s connection timeout: waiting for connection state change timed out", e.Name())
		case <-ticker.C:
			if symbolConn.conn.State() == core.StateDisconnected {
				if err := symbolConn.conn.Connect(ctx); err != nil {
					e.Disconnect(ctx, symbolConn)
					return gerror.Wrapf(err, "failed to connect to %s", e.Name())
				} else {
					break waitLoop
				}
			} else if symbolConn.conn.State() == core.StateConnected {
				break waitLoop
			} else if symbolConn.conn.State() == core.StateError {
				e.Disconnect(ctx, symbolConn)
				return gerror.Newf("failed to connect to %s", e.Name())
			}
		}
	}

	// 启动消息处理
	go e.handleMessages(ctx, symbolConn)

	// 发送订阅消息
	err = symbolConn.basePriceStream.Subscribe(ctx, symbol)
	if err != nil {
		return
	}

	// 启动心跳检查
	go func() {
		ticker := time.NewTicker(6 * time.Second)
		reconnectTicker := time.NewTicker(time.Hour)
		defer func() {
			reconnectTicker.Stop()
			ticker.Stop()
		}()

		for {
			select {
			case <-symbolConn.basePriceStream.ctx.Done():
				return
			case <-reconnectTicker.C:
				g.Log().Infof(ctx, "%s hourly disconnect and reconnect", e.Name())
				e.Reconnect(ctx, symbolConn)
				return
			case <-ticker.C:
				if err := e.Health(symbolConn); err != nil {
					g.Log().Errorf(ctx, "%s health check failed: %v", e.Name(), err)
					e.Reconnect(ctx, symbolConn)
					return
				}
			}
		}
	}()

	return
}

// Unsubscribe 取消订阅
func (e *baseExchange) Unsubscribe(ctx context.Context, symbol string) (err error) {
	e.connMu.Lock()
	defer e.connMu.Unlock()

	var currentConn *SymbolConn
	for _, symbolConn := range e.connPool {
		if _, ok := symbolConn.symbols[symbol]; ok {
			currentConn = symbolConn
		}
	}

	if currentConn == nil {
		return
	}

	err = currentConn.basePriceStream.Unsubscribe(symbol)
	if err != nil {
		return
	}

	currentConn.symbolMu.Lock()
	delete(currentConn.symbols, symbol)
	currentConn.symbolMu.Unlock()

	if len(currentConn.symbols) == 0 {
		e.Disconnect(ctx, currentConn)
	}

	return
}

// Disconnect 断开连接
func (e *baseExchange) Disconnect(ctx context.Context, symbolConn *SymbolConn) {
	e.connMu.Lock()
	defer e.connMu.Unlock()

	if symbolConn.conn.State() == core.StateConnected {
		err := symbolConn.conn.Disconnect()
		if err != nil {
			g.Log().Warningf(ctx, "failed to disconnect from %s: %s", e.Name(), err)
		}
	}
	symbolConn.symbols = make(map[string]bool)
	_ = symbolConn.basePriceStream.Close()

	var newPool []*SymbolConn
	for _, val := range e.connPool {
		if val != symbolConn {
			newPool = append(newPool, val)
		}
	}
	e.connPool = newPool
}

// DisconnectAll 断开所有连接
func (e *baseExchange) DisconnectAll(ctx context.Context) error {
	var waitCloseConn []*SymbolConn
	e.connMu.Lock()
	for _, val := range e.connPool {
		waitCloseConn = append(waitCloseConn, val)
	}
	e.connMu.Unlock()

	for _, symbolConn := range waitCloseConn {
		e.Disconnect(ctx, symbolConn)
	}

	return nil
}

// Health 健康检查
func (e *baseExchange) Health(symbolConn *SymbolConn) error {
	if symbolConn.conn.State() != core.StateConnected {
		return gerror.Newf("%s exchange not connected", e.Name())
	}

	pingMsg, err := e.handler.BuildHealthCheckMsg()
	if err != nil {
		return gerror.Wrapf(err, "%s failed to build health check message", e.Name())
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = symbolConn.conn.Send(ctx, pingMsg)
	if err != nil {
		return gerror.Wrapf(err, "%s health check failed", e.Name())
	}

	return nil
}

// handleMessages 处理接收到的消息
func (e *baseExchange) handleMessages(ctx context.Context, symbolConn *SymbolConn) {
	for {
		select {
		case <-symbolConn.basePriceStream.ctx.Done():
			return
		case data := <-symbolConn.conn.Receive():
			e.handler.ProcessMessage(ctx, data, symbolConn)
		case err := <-symbolConn.conn.Errors():
			g.Log().Errorf(ctx, "%s connection error: %v", e.Name(), err)
			e.Reconnect(ctx, symbolConn)
			return
		}
	}
}

// Reconnect 重新连接
func (e *baseExchange) Reconnect(ctx context.Context, symbolConn *SymbolConn) {
	var symbols []string
	symbolConn.symbolMu.RLock()
	for symbol := range symbolConn.symbols {
		symbols = append(symbols, symbol)
	}
	symbolConn.symbolMu.RUnlock()

	e.Disconnect(ctx, symbolConn)

	if e.eventBus != nil {
		e.eventBus.Publish(
			core.Event{
				Type:      core.EventTypeConnectionError,
				Data:      symbols,
				Source:    e.Name(),
				Timestamp: time.Now(),
			},
		)
	}
}
