// Package exchange MEXC交易所实现
package exchange

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"

	"arbitrage/internal/core"
	"arbitrage/internal/infrastructure/websocket"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
)

// mexcExchange MEXC交易所实现
type mexcExchange struct {
	name                string
	connectionMaxSymbol int
	connPool            []*mexcSymbolConn
	connMu              sync.RWMutex
	eventBus            core.EventBus
}

type mexcSymbolConn struct {
	conn            core.Connection
	connected       bool
	symbols         map[string]bool // 监听的交易对
	symbolMu        sync.RWMutex
	basePriceStream *basePriceStream
}

// mexcPriceStream MEXC价格数据流实现
// 现在嵌入 basePriceStream，并实现 PriceStreamAdapter
type mexcPriceStream struct{}

// ToExchangeSymbol 符号转换为交易所格式
func (ps *mexcPriceStream) ToExchangeSymbol(symbol string) string {
	return gstr.ToUpper(symbol) + "_USDT"
}

// FromExchangeSymbol 交易所格式转为本地格式
func (ps *mexcPriceStream) FromExchangeSymbol(symbol string) string {
	pos := gstr.Pos(gstr.ToUpper(symbol), "_USDT")
	if pos == -1 {
		return symbol
	}
	return symbol[:pos]
}

// BuildSubscribeMsg 构造订阅消息
func (ps *mexcPriceStream) BuildSubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"method": "sub.kline",
		"param": map[string]string{
			"symbol":   ps.ToExchangeSymbol(symbol),
			"interval": "Min1",
		},
	}
	return json.Marshal(msg)
}

// BuildUnsubscribeMsg 构造取消订阅消息
func (ps *mexcPriceStream) BuildUnsubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"method": "unsub.kline",
		"param": map[string]string{
			"symbol":   ps.ToExchangeSymbol(symbol),
			"interval": "Min1",
		},
	}
	return json.Marshal(msg)
}

// mexcMessage MEXC消息格式
type mexcMessage struct {
	Symbol  string      `json:"symbol"`
	Data    interface{} `json:"data"`
	Channel string      `json:"channel"`
	Ts      int64       `json:"ts"`
}

// mexcKlineData MEXC K线数据
type mexcKlineData struct {
	Symbol   string  `json:"symbol"`
	Interval string  `json:"interval"`
	T        int64   `json:"t"`
	O        float64 `json:"o"`
	C        float64 `json:"c"`
	H        float64 `json:"h"`
	L        float64 `json:"l"`
	A        float64 `json:"a"`
	Q        float64 `json:"q"`
	Ro       float64 `json:"ro"`
	Rc       float64 `json:"rc"`
	Rh       float64 `json:"rh"`
	Rl       float64 `json:"rl"`
}

// NewMexcExchange 创建MEXC交易所实例
func NewMexcExchange(eventBus core.EventBus) core.Exchange {
	exchange := &mexcExchange{
		name:                "mexc",
		eventBus:            eventBus,
		connectionMaxSymbol: 10,
	}

	return exchange
}

func (e *mexcExchange) initConnection() core.Connection {
	config := core.ConnectionConfig{
		URL:               "wss://futures.mexc.com/edge",
		ReconnectInterval: 5 * time.Second,
		MaxReconnectTries: 10,
		PingInterval:      30 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
	}

	return websocket.NewConnection(config)
}

// Name 返回交易所名称
func (e *mexcExchange) Name() string {
	return e.name
}

func (e *mexcExchange) Subscribe(ctx context.Context, symbol string) (err error) {
	maxSymbol := e.connectionMaxSymbol

	e.connMu.Lock()
	var symbolConn *mexcSymbolConn
	for _, val := range e.connPool {
		if len(val.symbols) >= maxSymbol {
			continue
		} else {
			symbolConn = val
		}
	}

	if symbolConn == nil {
		cancelCtx, cancel := context.WithCancel(ctx)
		symbolConn = &mexcSymbolConn{
			symbols: map[string]bool{symbol: true},
			conn:    e.initConnection(),
		}

		symbolConn.basePriceStream = &basePriceStream{
			adapter: &mexcPriceStream{},
			ctx:     cancelCtx,
			cancel:  cancel,
			sendFunc: func(ctx context.Context, data []byte) error {
				if symbolConn.conn == nil {
					return gerror.New("MEXC连接未建立")
				}

				return symbolConn.conn.Send(ctx, data)
			},
		}

		e.connPool = append(e.connPool, symbolConn)
	} else {
		symbolConn.symbolMu.Lock()
		symbolConn.symbols[symbol] = true
		symbolConn.symbolMu.Unlock()
	}
	e.connMu.Unlock()

	for {
		if symbolConn.conn.State() == core.StateConnecting {
			time.Sleep(time.Second)
		} else {
			break
		}
	}

	if !e.IsConnected(symbolConn) {
		if err := symbolConn.conn.Connect(ctx); err != nil {
			return gerror.Wrap(err, "连接MEXC失败")
		}
	}

	// 发送订阅消息
	err = symbolConn.basePriceStream.Subscribe(ctx, symbol)
	if err != nil {
		return
	}

	// 启动消息处理
	go e.handleMessages(ctx, symbolConn)

	// 启动心跳检查
	go func() {
		ticker := time.NewTicker(6 * time.Second)
		reconnectTicker := time.NewTicker(time.Hour)
		defer func() {
			reconnectTicker.Stop()
			ticker.Stop()
		}()

		for {
			select {
			case <-symbolConn.basePriceStream.ctx.Done():
				return
			case <-reconnectTicker.C:
				g.Log().Info(ctx, "MEXC每小时断开重连")

				e.Reconnect(ctx, symbolConn)

				return
			case <-ticker.C:
				if err := e.Health(symbolConn); err != nil {
					g.Log().Errorf(ctx, "MEXC心跳检查失败: %v", err)

					e.Reconnect(ctx, symbolConn)
					return
				}
			}
		}
	}()

	return
}

func (e *mexcExchange) Unsubscribe(ctx context.Context, symbol string) (err error) {
	e.connMu.Lock()
	defer e.connMu.Unlock()

	var currentConn *mexcSymbolConn
	for _, symbolConn := range e.connPool {
		if _, ok := symbolConn.symbols[symbol]; ok {
			currentConn = symbolConn
		}
	}

	if currentConn == nil {
		return
	}

	err = currentConn.basePriceStream.Unsubscribe(symbol)
	if err != nil {
		return
	}

	currentConn.symbolMu.Lock()
	delete(currentConn.symbols, symbol)
	currentConn.symbolMu.Unlock()

	if len(currentConn.symbols) == 0 {
		e.Disconnect(ctx, currentConn)
	}

	return
}

// Disconnect 断开连接
func (e *mexcExchange) Disconnect(ctx context.Context, symbolConn *mexcSymbolConn) {
	e.connMu.RLock()
	symbolConn.symbolMu.RLock()
	defer func() {
		e.connMu.RUnlock()
		symbolConn.symbolMu.RUnlock()
	}()

	err := symbolConn.conn.Disconnect()
	if err != nil {
		g.Log().Warningf(ctx, "断开MEXC连接失败: %s", err)
	}
	symbolConn.symbols = make(map[string]bool)
	symbolConn.basePriceStream.cancel()

	var newPool []*mexcSymbolConn
	for _, val := range e.connPool {
		if val != symbolConn {
			newPool = append(newPool, val)
		}
	}

	return
}

func (e *mexcExchange) DisconnectAll(ctx context.Context) error {
	var waitCloseConn []*mexcSymbolConn
	e.connMu.Lock()
	for _, val := range e.connPool {
		waitCloseConn = append(waitCloseConn, val)
	}
	e.connMu.Unlock()

	for _, symbolConn := range waitCloseConn {
		e.Disconnect(ctx, symbolConn)
	}

	return nil
}

// IsConnected 检查连接状态
func (e *mexcExchange) IsConnected(symbolConn *mexcSymbolConn) bool {
	return symbolConn.conn.State() == core.StateConnected
}

// Health 健康检查
func (e *mexcExchange) Health(symbolConn *mexcSymbolConn) error {
	if !e.IsConnected(symbolConn) {
		return gerror.New("MEXC交易所未连接")
	}

	// 发送JSON格式的ping消息
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pingMsg := []byte(`{"method":"ping"}`)
	err := symbolConn.conn.Send(ctx, pingMsg)
	if err != nil {
		return gerror.Wrap(err, "MEXC健康检查失败")
	}

	return nil
}

// handleMessages 处理接收到的消息
func (e *mexcExchange) handleMessages(ctx context.Context, symbolConn *mexcSymbolConn) {
	for {
		select {
		case <-symbolConn.basePriceStream.ctx.Done():
			return
		case data := <-symbolConn.conn.Receive():
			e.processMessage(ctx, data, symbolConn)
		case err := <-symbolConn.conn.Errors():
			g.Log().Errorf(ctx, "MEXC连接错误: %v\n", err)
			go e.Reconnect(ctx, symbolConn)
			return
		}
	}
}

// processMessage 处理消息
func (e *mexcExchange) processMessage(ctx context.Context, data []byte, symbolConn *mexcSymbolConn) {
	// 直接解析为MEXC消息格式
	var mexcMsg mexcMessage
	if err := json.Unmarshal(data, &mexcMsg); err != nil {
		g.Log().Errorf(ctx, "解析MEXC消息失败: %v\n", err)
		return
	}

	// 处理订阅响应
	if mexcMsg.Channel == "rs.sub.kline" {
		if mexcMsg.Data == "success" {
			g.Log().Info(ctx, "MEXC K线订阅成功")
		} else {
			g.Log().Infof(ctx, "MEXC K线订阅失败: %v\n", mexcMsg.Data)
		}
		return
	}

	// 处理K线数据
	if mexcMsg.Channel == "push.kline" {
		e.handleKlineData(ctx, mexcMsg, symbolConn)
	} else if mexcMsg.Channel == "rs.sub.kline" {
		g.Log().Infof(ctx, "Mexc Kline 订阅状态： %s", mexcMsg.Data)
	}
}

// handleKlineData 处理K线数据
func (e *mexcExchange) handleKlineData(ctx context.Context, mexcMsg mexcMessage, symbolConn *mexcSymbolConn) {
	var klineData mexcKlineData
	if err := gjson.DecodeTo(mexcMsg.Data, &klineData); err != nil {
		g.Log().Infof(ctx, "解析MEXC K线数据失败: %v\n", err)
		return
	}

	priceData := core.PriceData{
		Symbol:       symbolConn.basePriceStream.adapter.FromExchangeSymbol(klineData.Symbol),
		Price:        klineData.C,
		Timestamp:    gtime.New(klineData.T),
		MsgTimestamp: gtime.New(mexcMsg.Ts),
		RxTimestamp:  gtime.Now(),
		Source:       e.name,
	}

	if e.eventBus != nil {
		e.eventBus.Publish(
			core.Event{
				Type:      core.EventTypePriceUpdate,
				Data:      priceData,
				Timestamp: time.Now(),
				Source:    e.name,
			},
		)
	}
}

func (e *mexcExchange) Reconnect(ctx context.Context, symbolConn *mexcSymbolConn) {
	var symbols []string
	for symbol := range symbolConn.symbols {
		symbols = append(symbols, symbol)
	}

	e.Disconnect(ctx, symbolConn)

	if e.eventBus != nil {
		e.eventBus.Publish(
			core.Event{
				Type:      core.EventTypeConnectionError,
				Data:      symbols,
				Source:    e.name,
				Timestamp: time.Now(),
			},
		)
	}

	return
}
