// Package exchange MEXC交易所实现
package exchange

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"

	"arbitrage/internal/core"
)

// mexcHandler MEXC 交易所处理器
type mexcHandler struct {
	eventBus core.EventBus
}

// mexcPriceStreamAdapter MEXC价格数据流适配器
type mexcPriceStreamAdapter struct{}

// ToExchangeSymbol 符号转换为交易所格式
func (ps *mexcPriceStreamAdapter) ToExchangeSymbol(symbol string) string {
	return gstr.ToUpper(symbol) + "_USDT"
}

// FromExchangeSymbol 交易所格式转为本地格式
func (ps *mexcPriceStreamAdapter) FromExchangeSymbol(symbol string) string {
	pos := gstr.Pos(gstr.ToUpper(symbol), "_USDT")
	if pos == -1 {
		return symbol
	}
	return symbol[:pos]
}

// BuildSubscribeMsg 构造订阅消息
func (ps *mexcPriceStreamAdapter) BuildSubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"method": "sub.kline",
		"param": map[string]string{
			"symbol":   ps.ToExchangeSymbol(symbol),
			"interval": "Min1",
		},
	}
	return json.Marshal(msg)
}

// BuildUnsubscribeMsg 构造取消订阅消息
func (ps *mexcPriceStreamAdapter) BuildUnsubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"method": "unsub.kline",
		"param": map[string]string{
			"symbol":   ps.ToExchangeSymbol(symbol),
			"interval": "Min1",
		},
	}
	return json.Marshal(msg)
}

// mexcMessage MEXC消息格式
type mexcMessage struct {
	Symbol  string      `json:"symbol"`
	Data    interface{} `json:"data"`
	Channel string      `json:"channel"`
	Ts      int64       `json:"ts"`
}

// mexcKlineData MEXC K线数据
type mexcKlineData struct {
	Symbol   string  `json:"symbol"`
	Interval string  `json:"interval"`
	T        int64   `json:"t"`
	O        float64 `json:"o"`
	C        float64 `json:"c"`
	H        float64 `json:"h"`
	L        float64 `json:"l"`
	A        float64 `json:"a"`
	Q        float64 `json:"q"`
	Ro       float64 `json:"ro"`
	Rc       float64 `json:"rc"`
	Rh       float64 `json:"rh"`
	Rl       float64 `json:"rl"`
}

// NewMexcExchange 创建MEXC交易所实例
func NewMexcExchange(eventBus core.EventBus) core.Exchange {
	handler := &mexcHandler{
		eventBus: eventBus,
	}
	return NewBaseExchange(handler, eventBus, 10)
}

// Name returns the name of the exchange.
func (h *mexcHandler) Name() string {
	return "mexc"
}

// GetConnectionConfig returns the connection configuration for the exchange.
func (h *mexcHandler) GetConnectionConfig() core.ConnectionConfig {
	return core.ConnectionConfig{
		URL:               "wss://futures.mexc.com/edge",
		ReconnectInterval: 5 * time.Second,
		MaxReconnectTries: 10,
		PingInterval:      30 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
	}
}

// GetPriceStreamAdapter returns the price stream adapter for the exchange.
func (h *mexcHandler) GetPriceStreamAdapter() PriceStreamAdapter {
	return &mexcPriceStreamAdapter{}
}

// BuildHealthCheckMsg builds the health check message for the exchange.
func (h *mexcHandler) BuildHealthCheckMsg() ([]byte, error) {
	return []byte(`{"method":"ping"}`), nil
}

var mexcMsgPool = sync.Pool{
	New: func() interface{} {
		return new(mexcMessage)
	},
}

// ProcessMessage processes incoming messages from the exchange.
func (h *mexcHandler) ProcessMessage(ctx context.Context, data []byte, symbolConn *SymbolConn) {
	mexcMsg := mexcMsgPool.Get().(*mexcMessage)
	defer func() {
		*mexcMsg = mexcMessage{} // 清理内部字段
		mexcMsgPool.Put(mexcMsg)
	}()

	if err := json.Unmarshal(data, &mexcMsg); err != nil {
		g.Log().Errorf(ctx, "解析MEXC消息失败: %v\n", err)
		return
	}

	// 处理订阅响应
	if mexcMsg.Channel == "rs.sub.kline" {
		if mexcMsg.Data == "success" {
			g.Log().Info(ctx, "MEXC K线订阅成功")
		} else {
			g.Log().Infof(ctx, "MEXC K线订阅失败: %v\n", mexcMsg.Data)
		}
		return
	} else if mexcMsg.Channel == "push.kline" {
		h.handleKlineData(ctx, *mexcMsg, symbolConn)
	}
}

// handleKlineData 处理K线数据
func (h *mexcHandler) handleKlineData(ctx context.Context, mexcMsg mexcMessage, symbolConn *SymbolConn) {
	var klineData mexcKlineData
	if err := gjson.DecodeTo(mexcMsg.Data, &klineData); err != nil {
		g.Log().Infof(ctx, "解析MEXC K线数据失败: %v\n", err)
		return
	}

	priceData := core.PriceData{
		Symbol:       symbolConn.BasePriceStream().adapter.FromExchangeSymbol(klineData.Symbol),
		Price:        klineData.C,
		Timestamp:    gtime.New(klineData.T),
		MsgTimestamp: gtime.New(mexcMsg.Ts),
		RxTimestamp:  gtime.Now(),
		Source:       h.Name(),
	}

	if h.eventBus != nil {
		h.eventBus.Publish(
			core.Event{
				Type:      core.EventTypePriceUpdate,
				Data:      priceData,
				Timestamp: time.Now(),
				Source:    h.Name(),
			},
		)
	}
}
