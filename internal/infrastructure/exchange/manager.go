// Package exchange 交易所管理器实现
package exchange

import (
	"context"
	"sync"

	"arbitrage/internal/core"

	"github.com/gogf/gf/v2/errors/gerror"
)

// manager 实现 core.ExchangeManager，负责管理多个交易所实例。
type manager struct {
	exchanges map[string]core.Exchange
	mu        sync.RWMutex
}

// NewManager 创建并返回一个新的交易所管理器。
func NewManager() core.ExchangeManager {
	return &manager{
		exchanges: make(map[string]core.Exchange),
	}
}

// RegisterExchange 注册一个新的交易所实例。
func (m *manager) RegisterExchange(exchange core.Exchange) error {
	if exchange == nil {
		return gerror.New("交易所不能为空")
	}
	name := exchange.Name()
	if name == "" {
		return gerror.New("交易所名称不能为空")
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	if _, exists := m.exchanges[name]; exists {
		return gerror.Newf("交易所 %s 已存在", name)
	}
	m.exchanges[name] = exchange
	return nil
}

// GetExchange 根据名称获取交易所实例。
func (m *manager) GetExchange(name string) (core.Exchange, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	ex, exists := m.exchanges[name]
	if !exists {
		return nil, gerror.Newf("交易所 %s 不存在", name)
	}
	return ex, nil
}

// GetAllExchanges 返回所有已注册的交易所实例。
func (m *manager) GetAllExchanges() []core.Exchange {
	m.mu.RLock()
	defer m.mu.RUnlock()
	result := make([]core.Exchange, 0, len(m.exchanges))
	for _, ex := range m.exchanges {
		result = append(result, ex)
	}
	return result
}

// DisconnectAll 并发断开所有已注册的交易所。
func (m *manager) DisconnectAll(ctx context.Context) error {
	return m.parallelForEachExchange(
		func(ex core.Exchange) error {
			if err := ex.DisconnectAll(ctx); err != nil {
				return gerror.Wrapf(err, "断开交易所 %s 连接失败", ex.Name())
			}
			return nil
		},
	)
}

// parallelForEachExchange 并发执行操作并收集错误。
func (m *manager) parallelForEachExchange(action func(core.Exchange) error) error {
	m.mu.RLock()
	exchanges := make([]core.Exchange, 0, len(m.exchanges))
	for _, ex := range m.exchanges {
		exchanges = append(exchanges, ex)
	}
	m.mu.RUnlock()

	var wg sync.WaitGroup
	errCh := make(chan error, len(exchanges))
	for _, ex := range exchanges {
		wg.Add(1)
		go func(e core.Exchange) {
			defer wg.Done()
			if err := action(e); err != nil {
				errCh <- err
			}
		}(ex)
	}
	wg.Wait()
	close(errCh)

	return collectErrors(errCh)
}

// collectErrors 收集错误并合并为一个错误返回。
func collectErrors(errCh <-chan error) error {
	errs := make([]error, 0)
	for err := range errCh {
		errs = append(errs, err)
	}
	if len(errs) > 0 {
		return gerror.Newf("批量操作发生错误: %v", errs)
	}
	return nil
}
