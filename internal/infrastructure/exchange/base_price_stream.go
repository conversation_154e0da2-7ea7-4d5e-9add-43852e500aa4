package exchange

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

// basePriceStream 封装通用的价格流订阅、发布、关闭等逻辑
// 由各交易所 PriceStream 结构体嵌入
type basePriceStream struct {
	adapter  PriceStreamAdapter
	ctx      context.Context
	cancel   context.CancelFunc
	sendFunc func(ctx context.Context, data []byte) error // 由交易所注入
}

// Subscribe 订阅价格数据
func (bps *basePriceStream) Subscribe(ctx context.Context, symbol string) error {
	msg, err := bps.adapter.BuildSubscribeMsg(symbol)
	if err != nil {
		return gerror.Wrap(err, "构造订阅消息失败")
	}

	if bps.sendFunc == nil {
		return gerror.New("sendFunc 未设置")
	}

	if err := bps.sendFunc(ctx, msg); err != nil {
		return gerror.Wrap(err, "发送订阅消息失败")
	}

	return nil
}

// Unsubscribe 取消订阅
func (bps *basePriceStream) Unsubscribe(symbol string) error {
	msg, err := bps.adapter.BuildUnsubscribeMsg(symbol)
	if err != nil {
		return gerror.Wrap(err, "构造取消订阅消息失败")
	}

	if bps.sendFunc == nil {
		return gerror.New("sendFunc 未设置")
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	return bps.sendFunc(ctx, msg)
}

// Close 关闭所有订阅
func (bps *basePriceStream) Close() error {
	if bps.cancel != nil {
		bps.cancel()
	}
	return nil
}
