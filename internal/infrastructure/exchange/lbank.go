// Package exchange LBank交易所实现
package exchange

import (
	"bytes"
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"

	"arbitrage/internal/core"
)

// lbankHandler LBank 交易所处理器
type lbankHandler struct {
	eventBus core.EventBus
}

// lbankPriceStreamAdapter LBank价格数据流适配器
type lbankPriceStreamAdapter struct{}

// ToExchangeSymbol 符号转换为交易所格式
func (ps *lbankPriceStreamAdapter) ToExchangeSymbol(symbol string) string {
	return gstr.ToUpper(symbol) + "USDT_1m"
}

// FromExchangeSymbol 交易所格式转为本地格式
func (ps *lbankPriceStreamAdapter) FromExchangeSymbol(symbol string) string {
	pos := gstr.Pos(gstr.ToUpper(symbol), "USDT")
	if pos == -1 {
		return symbol
	}
	return symbol[:pos]
}

// BuildSubscribeMsg 构造订阅消息
func (ps *lbankPriceStreamAdapter) BuildSubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"x": 2,
		"y": "**********",
		"a": map[string]interface{}{
			"i": ps.ToExchangeSymbol(symbol),
		},
		"z": 1,
	}
	return json.Marshal(msg)
}

// BuildUnsubscribeMsg 构造取消订阅消息
func (ps *lbankPriceStreamAdapter) BuildUnsubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"action":    "unsubscribe",
		"subscribe": "kline",
		"pair":      symbol,
	}
	return json.Marshal(msg)
}

// lbankMessage LBank消息格式
type lbankMessage struct {
	TimestampMs int64       `json:"w" dc:"时间戳，毫秒"`
	X           int         `json:"x"`
	Y           string      `json:"y"`
	Z           int         `json:"z"`
	Data        interface{} `json:"d"`
}

// lbankKlineData LBank K线数据
type lbankKlineData struct {
	Symbol           string `json:"a" dc:"交易对"`
	Interval         string `json:"b" dc:"周期"`
	Timestamp        int64  `json:"c" dc:"时间戳，秒"`
	Open             string `json:"d" dc:"open price"`
	Close            string `json:"e" dc:"close price"`
	High             string `json:"f" dc:"high price"`
	Low              string `json:"g" dc:"low price"`
	Volume           string `json:"h" dc:"成交量"`
	Amount           string `json:"i" dc:"成交额"`
	J                string `json:"j"`
	TimestampCurrent int64  `json:"k" dc:"时间戳，毫秒"`
}

// NewLBankExchange 创建LBank交易所实例
func NewLBankExchange(eventBus core.EventBus) core.Exchange {
	handler := &lbankHandler{
		eventBus: eventBus,
	}
	return NewBaseExchange(handler, eventBus, 1)
}

// Name returns the name of the exchange.
func (h *lbankHandler) Name() string {
	return "lbank"
}

// GetConnectionConfig returns the connection configuration for the exchange.
func (h *lbankHandler) GetConnectionConfig() core.ConnectionConfig {
	return core.ConnectionConfig{
		URL:               "wss://uuws.ierpifvid.com/ws/v3",
		ReconnectInterval: 5 * time.Second,
		MaxReconnectTries: 10,
		PingInterval:      30 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
	}
}

// GetPriceStreamAdapter returns the price stream adapter for the exchange.
func (h *lbankHandler) GetPriceStreamAdapter() PriceStreamAdapter {
	return &lbankPriceStreamAdapter{}
}

// BuildHealthCheckMsg builds the health check message for the exchange.
func (h *lbankHandler) BuildHealthCheckMsg() ([]byte, error) {
	return []byte("ping"), nil
}

var lbankMsgPool = sync.Pool{
	New: func() interface{} {
		return new(lbankMessage)
	},
}

// ProcessMessage processes incoming messages from the exchange.
func (h *lbankHandler) ProcessMessage(ctx context.Context, data []byte, symbolConn *SymbolConn) {
	if bytes.EqualFold(data, []byte("pong")) {
		// 处理pong消息
		return
	}

	lbankMsg := lbankMsgPool.Get().(*lbankMessage)
	defer func() {
		*lbankMsg = lbankMessage{} // 清理内部字段
		lbankMsgPool.Put(lbankMsg)
	}()

	if err := json.Unmarshal(data, &lbankMsg); err != nil {
		g.Log().Errorf(ctx, "解析LBank消息失败: %v", err)
		return
	}

	// 处理K线数据
	if lbankMsg.X == 2 {
		h.handleKlineData(ctx, *lbankMsg, symbolConn)
	}
}

// handleKlineData 处理K线数据
func (h *lbankHandler) handleKlineData(ctx context.Context, lbankMsg lbankMessage, symbolConn *SymbolConn) {
	var kline interface{}

	// 检查Data字段是否为数组，不知道为啥，有时候是数组，有时候是对象
	// 如果是数组，取最后一个值
	klineDataArray, ok := lbankMsg.Data.([]interface{})
	if ok {
		if len(klineDataArray) == 0 {
			return
		}
		kline = klineDataArray[len(klineDataArray)-1]
	} else {
		kline = lbankMsg.Data
	}

	// 解析为K线数据结构
	var klineData lbankKlineData
	if err := gjson.DecodeTo(kline, &klineData); err != nil {
		g.Log().Errorf(ctx, "解析LBank K线数据失败: %v", err)
		return
	}

	// 将Close价格转换为float64
	closePrice := gconv.Float64(klineData.Close)

	priceData := core.PriceData{
		Symbol:       symbolConn.BasePriceStream().adapter.FromExchangeSymbol(klineData.Symbol),
		Price:        closePrice,
		Timestamp:    gtime.New(klineData.Timestamp),
		MsgTimestamp: gtime.New(lbankMsg.TimestampMs),
		RxTimestamp:  gtime.Now(),
		Source:       h.Name(),
	}

	if h.eventBus != nil {
		h.eventBus.Publish(
			core.Event{
				Type:      core.EventTypePriceUpdate,
				Data:      priceData,
				Timestamp: time.Now(),
				Source:    h.Name(),
			},
		)
	}
}
