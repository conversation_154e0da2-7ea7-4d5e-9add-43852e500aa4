// Package exchange LBank交易所实现
package exchange

import (
	"context"
	"encoding/json"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"

	"arbitrage/internal/core"
	"arbitrage/internal/infrastructure/websocket"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// lbankExchange LBank交易所实现
type lbankExchange struct {
	name                string
	connectionMaxSymbol int
	connPool            []*lbankSymbolConn
	connMu              sync.RWMutex
	eventBus            core.EventBus
}

// lbankSymbolConn 维护每个symbol的ws连接、通道、ctx、cancel
type lbankSymbolConn struct {
	conn            core.Connection
	symbols         map[string]bool // 监听的交易对
	symbolMu        sync.RWMutex
	basePriceStream *basePriceStream
}

// lbankPriceStream LBank价格数据流实现
// 现在每个symbol独立ws连接
type lbankPriceStream struct{}

// lbankMessage LBank消息格式
type lbankMessage struct {
	TimestampMs int64       `json:"w" dc:"时间戳，毫秒"`
	X           int         `json:"x"`
	Y           string      `json:"y"`
	Z           int         `json:"z"`
	Data        interface{} `json:"d"`
}

// lbankKlineData LBank K线数据
type lbankKlineData struct {
	Symbol           string `json:"a" dc:"交易对"`
	Interval         string `json:"b" dc:"周期"`
	Timestamp        int64  `json:"c" dc:"时间戳，秒"`
	Open             string `json:"d" dc:"open price"`
	Close            string `json:"e" dc:"close price"`
	High             string `json:"f" dc:"high price"`
	Low              string `json:"g" dc:"low price"`
	Volume           string `json:"h" dc:"成交量"`
	Amount           string `json:"i" dc:"成交额"`
	J                string `json:"j"`
	TimestampCurrent int64  `json:"k" dc:"时间戳，毫秒"`
}

// ToExchangeSymbol 符号转换为交易所格式
func (ps *lbankPriceStream) ToExchangeSymbol(symbol string) string {
	return gstr.ToUpper(symbol) + "USDT_1m"
}

// FromExchangeSymbol 交易所格式转为本地格式
func (ps *lbankPriceStream) FromExchangeSymbol(symbol string) string {
	pos := gstr.Pos(gstr.ToUpper(symbol), "USDT")
	if pos == -1 {
		return symbol
	}
	return symbol[:pos]
}

// BuildSubscribeMsg 构造订阅消息
func (ps *lbankPriceStream) BuildSubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"x": 2,
		"y": "**********",
		"a": map[string]interface{}{
			"i": ps.ToExchangeSymbol(symbol),
		},
		"z": 1,
	}
	return json.Marshal(msg)
}

// BuildUnsubscribeMsg 构造取消订阅消息
func (ps *lbankPriceStream) BuildUnsubscribeMsg(symbol string) ([]byte, error) {
	msg := map[string]interface{}{
		"action":    "unsubscribe",
		"subscribe": "kline",
		"pair":      symbol,
	}
	return json.Marshal(msg)
}

// NewLBankExchange 创建LBank交易所实例
func NewLBankExchange(eventBus core.EventBus) core.Exchange {
	exchange := &lbankExchange{
		name:                "lbank",
		connPool:            []*lbankSymbolConn{},
		connectionMaxSymbol: 1,
		eventBus:            eventBus,
	}

	return exchange
}

// Name 返回交易所名称
func (e *lbankExchange) Name() string {
	return e.name
}

func (e *lbankExchange) initConnection() core.Connection {
	config := core.ConnectionConfig{
		URL:               "wss://uuws.ierpifvid.com/ws/v3",
		ReconnectInterval: 5 * time.Second,
		MaxReconnectTries: 10,
		PingInterval:      30 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
	}

	return websocket.NewConnection(config)
}

func (e *lbankExchange) Subscribe(ctx context.Context, symbol string) (err error) {
	maxSymbol := e.connectionMaxSymbol

	e.connMu.Lock()
	var symbolConn *lbankSymbolConn
	for _, val := range e.connPool {
		if len(val.symbols) >= maxSymbol {
			continue
		} else {
			symbolConn = val
		}
	}

	if symbolConn == nil {
		cancelCtx, cancel := context.WithCancel(ctx)
		symbolConn = &lbankSymbolConn{
			symbols: map[string]bool{symbol: true},
			conn:    e.initConnection(),
		}

		symbolConn.basePriceStream = &basePriceStream{
			adapter: &lbankPriceStream{},
			ctx:     cancelCtx,
			cancel:  cancel,
			sendFunc: func(ctx context.Context, data []byte) error {
				if symbolConn.conn == nil {
					return gerror.New("LBank连接未建立")
				}

				return symbolConn.conn.Send(ctx, data)
			},
		}

		e.connPool = append(e.connPool, symbolConn)
	} else {
		symbolConn.symbolMu.Lock()
		symbolConn.symbols[symbol] = true
		symbolConn.symbolMu.Unlock()
	}
	e.connMu.Unlock()

	for {
		if symbolConn.conn.State() == core.StateConnecting {
			time.Sleep(time.Second)
		} else {
			break
		}
	}

	if !e.IsConnected(symbolConn) {
		if err := symbolConn.conn.Connect(ctx); err != nil {
			return gerror.Wrap(err, "连接LBank失败")
		}
	}

	// 订阅
	err = symbolConn.basePriceStream.Subscribe(ctx, symbol)
	if err != nil {
		return
	}

	// 启动消息处理
	go e.handleMessages(ctx, symbolConn)

	// 启动心跳检查
	go func() {
		ticker := time.NewTicker(6 * time.Second)
		reconnectTicker := time.NewTicker(time.Hour)
		defer func() {
			reconnectTicker.Stop()
			ticker.Stop()
		}()

		for {
			select {
			case <-symbolConn.basePriceStream.ctx.Done():
				return
			case <-reconnectTicker.C:
				g.Log().Info(ctx, "LBank每小时断开重连")

				e.Reconnect(ctx, symbolConn)

				return
			case <-ticker.C:
				if err := e.Health(symbolConn); err != nil {
					g.Log().Errorf(ctx, "LBank心跳检查失败: %v", err)

					e.Reconnect(ctx, symbolConn)
					return
				}
			}
		}
	}()

	return
}

func (e *lbankExchange) Unsubscribe(ctx context.Context, symbol string) (err error) {
	e.connMu.Lock()
	defer e.connMu.Unlock()

	var currentConn *lbankSymbolConn
	for _, symbolConn := range e.connPool {
		if _, ok := symbolConn.symbols[symbol]; ok {
			currentConn = symbolConn
		}
	}

	if currentConn == nil {
		return
	}

	err = currentConn.basePriceStream.Unsubscribe(symbol)
	if err != nil {
		return
	}

	currentConn.symbolMu.Lock()
	delete(currentConn.symbols, symbol)
	currentConn.symbolMu.Unlock()

	if len(currentConn.symbols) == 0 {
		e.Disconnect(ctx, currentConn)
	}

	return
}

// Disconnect 断开连接
func (e *lbankExchange) Disconnect(ctx context.Context, symbolConn *lbankSymbolConn) {
	e.connMu.RLock()
	symbolConn.symbolMu.RLock()
	defer func() {
		e.connMu.RUnlock()
		symbolConn.symbolMu.RUnlock()
	}()

	err := symbolConn.conn.Disconnect()
	if err != nil {
		g.Log().Warningf(ctx, "断开LBANK连接失败: %s", err)
	}
	symbolConn.symbols = make(map[string]bool)
	symbolConn.basePriceStream.cancel()

	var newPool []*lbankSymbolConn
	for _, val := range e.connPool {
		if val != symbolConn {
			newPool = append(newPool, val)
		}
	}

	e.connPool = newPool

	return
}

func (e *lbankExchange) DisconnectAll(ctx context.Context) error {
	var waitCloseConn []*lbankSymbolConn
	e.connMu.Lock()
	for _, val := range e.connPool {
		waitCloseConn = append(waitCloseConn, val)
	}
	e.connMu.Unlock()

	for _, symbolConn := range waitCloseConn {
		e.Disconnect(ctx, symbolConn)
	}

	return nil
}

// IsConnected 检查连接状态
func (e *lbankExchange) IsConnected(symbolConn *lbankSymbolConn) bool {
	return symbolConn.conn.State() == core.StateConnected
}

// Health 健康检查
func (e *lbankExchange) Health(symbolConn *lbankSymbolConn) error {
	if !e.IsConnected(symbolConn) {
		return gerror.New("LBank交易所未连接")
	}

	// 发送文本格式的ping消息
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := symbolConn.conn.Send(ctx, []byte("ping"))
	if err != nil {
		return gerror.Wrapf(err, "LBank健康检查失败")
	}

	return nil
}

// handleMessages 处理接收到的消息
func (e *lbankExchange) handleMessages(ctx context.Context, symbolConn *lbankSymbolConn) {
	for {
		select {
		case <-symbolConn.basePriceStream.ctx.Done():
			return
		case data := <-symbolConn.conn.Receive():
			e.processMessage(ctx, data, symbolConn)
		case err := <-symbolConn.conn.Errors():
			g.Log().Errorf(ctx, "LBank连接错误: %v", err)

			e.Reconnect(ctx, symbolConn)

			return
		}
	}
}

// processMessage 处理消息
func (e *lbankExchange) processMessage(ctx context.Context, data []byte, symbolConn *lbankSymbolConn) {
	// 直接解析为LBank消息格式
	var lbankMsg lbankMessage

	if strings.ToLower(string(data)) == "pong" {
		// 处理pong消息
		return
	}

	if err := json.Unmarshal(data, &lbankMsg); err != nil {
		g.Log().Errorf(ctx, "解析LBank消息失败: %v", err)
		return
	}

	// 处理K线数据
	if lbankMsg.X == 2 {
		e.handleKlineData(ctx, lbankMsg, symbolConn)
	}
}

// handleKlineData 处理K线数据
func (e *lbankExchange) handleKlineData(ctx context.Context, lbankMsg lbankMessage, symbolConn *lbankSymbolConn) {
	var kline interface{}

	// 检查Data字段是否为数组，不知道为啥，有时候是数组，有时候是对象
	// 如果是数组，取最后一个值
	klineDataArray, ok := lbankMsg.Data.([]interface{})
	if ok {
		kline = klineDataArray[len(klineDataArray)-1]
	} else {
		kline = lbankMsg.Data
	}

	// 解析为K线数据结构
	var klineData lbankKlineData
	if err := gjson.DecodeTo(kline, &klineData); err != nil {
		g.Log().Errorf(ctx, "解析LBank K线数据失败: %v", err)
		return
	}

	// 将Close价格转换为float64
	closePrice := gconv.Float64(klineData.Close)

	g.Log().Debugf(
		ctx, "LBank %s 价格: %f, 时间: %s", klineData.Symbol, closePrice,
		gtime.New(lbankMsg.TimestampMs).Format("Y-m-d H:i:s.u"),
	)

	priceData := core.PriceData{
		Symbol:       symbolConn.basePriceStream.adapter.FromExchangeSymbol(klineData.Symbol),
		Price:        closePrice,
		Timestamp:    gtime.New(klineData.Timestamp),
		MsgTimestamp: gtime.New(lbankMsg.TimestampMs),
		RxTimestamp:  gtime.Now(),
		Source:       e.name,
	}

	if e.eventBus != nil {
		e.eventBus.Publish(
			core.Event{
				Type:      core.EventTypePriceUpdate,
				Data:      priceData,
				Timestamp: time.Now(),
				Source:    e.name,
			},
		)
	}
}

func (e *lbankExchange) Reconnect(ctx context.Context, symbolConn *lbankSymbolConn) {
	var symbols []string
	for symbol := range symbolConn.symbols {
		symbols = append(symbols, symbol)
	}

	e.Disconnect(ctx, symbolConn)

	if e.eventBus != nil {
		e.eventBus.Publish(
			core.Event{
				Type:      core.EventTypeConnectionError,
				Data:      symbols,
				Source:    e.name,
				Timestamp: time.Now(),
			},
		)
	}
}
