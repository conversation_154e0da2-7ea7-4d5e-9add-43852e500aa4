# 统一默认日志配置
defaultLogger: &defaultLogger
  level: "all"
  flags: 42
  stdout: true
  file: "{Y-m-d}.log"                     # 日志文件格式。默认为"{Y-m-d}.log"
  ctxKeys: [ "RequestId" ]
  stdoutColorDisabled: false              # 关闭终端的颜色打印。可选：false|true，默认false
  writerColorEnable: false                # 日志文件是否带上颜色。可选：false|true，默认false，表示不带颜色
  rotateSize: "10M"
  rotateExpire: "7d"                      # 日志保留天数
  rotateBackupLimit: 20                   # 最大备份数量
  rotateBackupCompress: 2                 # 日志文件压缩级别，0-9,9最高

# https://goframe.org/docs/web/server-config-file-template
#server:
#  address:     ":8000"
#  openapiPath: "/api.json"
#  swaggerPath: "/swagger"

# https://goframe.org/docs/core/glog-config
logger:
  path: "./logs/default"
  <<: *defaultLogger

# https://goframe.org/docs/core/gdb-config-file
#database:
#  default:
#    link: "mysql:root:12345678@tcp(127.0.0.1:3306)/test"

monitor:
  - symbol: BNB
    priceJumpThreshold: 0.001
    priceEpsilon: 0.00005
    delayThreshold: 200
