package matcher

import (
	"testing"
	"time"
)

func TestJumpMatch_Success(t *testing.T) {
	m := &Matcher{}
	base := time.Now()

	// Binance 连续微变，最后跳变
	m.AddTick(PriceTick{100, base.Add(0 * time.Millisecond)}, "binance")
	m.AddTick(PriceTick{100.2, base.Add(500 * time.Millisecond)}, "binance")
	m.AddTick(PriceTick{100.3, base.Add(1000 * time.Millisecond)}, "binance")
	m.AddTick(PriceTick{106.0, base.Add(2000 * time.Millisecond)}, "binance") // 跳变

	// OKX 稍后匹配价格
	m.AddTick(PriceTick{100, base.Add(0 * time.Millisecond)}, "okx")
	m.AddTick(PriceTick{106.1, base.Add(2300 * time.Millisecond)}, "okx") // 延迟300ms，匹配成功
}

func TestJumpMatch_NoJump(t *testing.T) {
	m := &Matcher{}
	base := time.Now()

	// Binance 无明显跳变
	m.AddTick(PriceTick{100, base.Add(0 * time.Millisecond)}, "binance")
	m.AddTick(PriceTick{100.3, base.Add(1000 * time.Millisecond)}, "binance")
	m.AddTick(PriceTick{100.6, base.Add(2000 * time.Millisecond)}, "binance") // Δ0.3，未超过 JumpThreshold

	// OKX 有类似价格
	m.AddTick(PriceTick{100.6, base.Add(2100 * time.Millisecond)}, "okx") // 但不应触发匹配
}

func TestJumpMatch_TooLate(t *testing.T) {
	m := &Matcher{}
	base := time.Now()

	m.AddTick(PriceTick{100, base.Add(0)}, "binance")
	m.AddTick(PriceTick{105, base.Add(1000 * time.Millisecond)}, "binance") // 跳变

	m.AddTick(PriceTick{105, base.Add(5000 * time.Millisecond)}, "okx") // 延迟太久（>2s），应不匹配
}
