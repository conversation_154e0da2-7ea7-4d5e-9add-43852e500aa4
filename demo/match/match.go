package matcher

import (
	"fmt"
	"math"
	"time"
)

type PriceTick struct {
	Price     float64
	Timestamp time.Time
}

const (
	JumpThreshold = 5.0             // 价格跳变触发值
	PriceEpsilon  = 0.5             // 匹配误差范围
	MatchWindow   = 2 * time.Second // 允许延迟范围
)

type Matcher struct {
	BinanceTicks []PriceTick
	OkxTicks     []PriceTick
}

func (m *Matcher) AddTick(tick PriceTick, source string) {
	if source == "binance" {
		m.BinanceTicks = append(m.BinanceTicks, tick)
		m.checkJumpAndMatch(m.BinanceTicks, m.OkxTicks, tick, "binance", "okx")
		m.trimOld(&m.BinanceTicks, tick.Timestamp)
	} else {
		m.OkxTicks = append(m.OkxTicks, tick)
		m.checkJumpAndMatch(m.OkxTicks, m.BinanceTicks, tick, "okx", "binance")
		m.trimOld(&m.OkxTicks, tick.Timestamp)
	}
}

func (m *Matcher) trimOld(window *[]PriceTick, now time.Time) {
	cutoff := now.Add(-MatchWindow)
	i := 0
	for ; i < len(*window); i++ {
		if (*window)[i].Timestamp.After(cutoff) {
			break
		}
	}
	*window = (*window)[i:]
}

func (m *Matcher) checkJumpAndMatch(
	ownWindow, otherWindow []PriceTick,
	newTick PriceTick,
	ownSource, otherSource string,
) {
	n := len(ownWindow)
	if n < 2 {
		return
	}

	last := ownWindow[n-2]
	if math.Abs(newTick.Price-last.Price) < JumpThreshold {
		return
	}

	// 是跳变，尝试找另一边匹配
	for _, other := range otherWindow {
		if math.Abs(other.Price-newTick.Price) <= PriceEpsilon {
			delay := newTick.Timestamp.Sub(other.Timestamp)
			fmt.Printf("[%s ➡ %s] 跳变价格 %.2f，延迟：%v\n", ownSource, otherSource, newTick.Price, delay)
			return
		}
	}
}
