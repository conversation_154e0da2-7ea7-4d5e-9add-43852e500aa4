import csv
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import matplotlib.font_manager as fm
from matplotlib import rcParams
from matplotlib.ticker import FormatStrFormatter



def load_file(file_path):
    maxRate = 0.0
    lastPrice = 0.0
    maxRateTime = ''
    with open(file_path, 'r') as f:
            reader = csv.reader(f)
            header = next(reader, None)  # 跳过表头
            for row in reader:
                if not row or len(row) < 2:
                    continue
                try:
                    price = float(row[0])

                    if lastPrice > 0:
                        changeRate = abs(price - lastPrice)/lastPrice
                        print(f'时间：{row[3]} 价格: {price} 波动: {changeRate}')

                        if changeRate > maxRate:
                            maxRate = changeRate
                            maxRateTime = row[3]

                    lastPrice = price

                    
                except Exception as e:
                    print("出现如下异常%s"%e)
                    continue
    print(f'最大波动值: {maxRate} 出现于: {maxRateTime}')


csv_path1 = r'/Users/<USER>/Project/go/arbitrage/data/2025-06-18/price_SUI_mexc.csv'
# csv_path2 = r'/Users/<USER>/Project/go/arbitrage/data/price_BNB_lbank.csv'
load_file(csv_path1)

print("Done")