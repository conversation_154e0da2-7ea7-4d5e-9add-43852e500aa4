import csv
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import matplotlib.font_manager as fm
from matplotlib import rcParams
from matplotlib.ticker import FormatStrFormatter

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']  # 优先使用的中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 读取价格数据文件（CSV，格式：价格,时间,K线时间,接收时间）
def load_price_data(file_path):
    dates = []
    prices = []
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        header = next(reader, None)  # 跳过表头
        for row in reader:
            if not row or len(row) < 2:
                continue
            try:
                price = float(row[0])
                # 解析“时间”字段
                dt = datetime.strptime(row[3], "%Y-%m-%d %H:%M:%S.%f")
                dates.append(dt)
                prices.append(price)
            except Exception:
                continue
    return dates, prices

# 加载两个价格数据文件
# 请根据实际文件路径修改
csv_path1 = r'/Users/<USER>/Project/go/arbitrage/data/2025-06-18/price_BNB_mexc.csv'
csv_path2 = r'/Users/<USER>/Project/go/arbitrage/data/2025-06-18/price_BNB_lbank.csv'
dates1, prices1 = load_price_data(csv_path1)
dates2, prices2 = load_price_data(csv_path2)

# 创建图表
plt.figure(figsize=(12, 6))

# 绘制两条价格线
plt.plot(dates1, prices1, 'b-', label='mexc', linewidth=1.5)
plt.plot(dates2, prices2, 'r-', label='lbank', linewidth=1.5)

# 设置图表标题和标签
plt.title('价格对比图', fontsize=16)
plt.xlabel('时间', fontsize=12)
plt.ylabel('价格', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.7)
plt.legend()

# 优化 x 轴显示
date_format = mdates.DateFormatter('%Y-%m-%d %H:%M:%S')
plt.gca().xaxis.set_major_formatter(date_format)
plt.gca().yaxis.set_major_formatter(FormatStrFormatter('%.2f'))  # 保留两位小数

plt.gcf().autofmt_xdate()

plt.tight_layout()

# 保存图表
# plt.savefig('price_comparison.png', dpi=300)

# 显示图表
plt.show()

# print("图表已保存为 price_comparison.png")
