import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d

# Load the price arrays
with open(r"D:\Project\quant\arbitrage-single\test\data\price1_ms.json") as f1, open(r"D:\Project\quant\arbitrage-single\test\data\price2_ms.json") as f2:
    price1 = json.load(f1)
    price2 = json.load(f2)

# Convert to pandas DataFrame
df1 = pd.DataFrame(price1)
df2 = pd.DataFrame(price2)

# Rename columns for clarity
df1.columns = ['Price1', 'Ts']
df2.columns = ['Price2', 'Ts']

# Convert timestamps to numeric (ms since epoch)
df1['Ts'] = pd.to_numeric(df1['Ts'])
df2['Ts'] = pd.to_numeric(df2['Ts'])

# Create interpolation function for price1
interp1 = interp1d(df1['Ts'], df1['Price1'], bounds_error=False, fill_value="extrapolate")

# Try lagging df2 by different values and compute mean squared error
lags = np.arange(-5000, 5001, 100)
mse_results = []

for lag in lags:
    shifted_ts = df2['Ts'] + lag
    interpolated_price1 = interp1(shifted_ts)
    mse = np.mean((interpolated_price1 - df2['Price2']) ** 2)
    mse_results.append((lag, mse))

# Find the lag with the smallest MSE
best_lag, best_mse = min(mse_results, key=lambda x: x[1])

# Plot MSE vs lag
plt.figure(figsize=(10, 5))
plt.plot([x[0] for x in mse_results], [x[1] for x in mse_results], marker='o')
plt.axvline(best_lag, color='red', linestyle='--', label=f'Best Lag = {best_lag} ms')
plt.title('Lag Detection via MSE')
plt.xlabel('Lag (ms)')
plt.ylabel('Mean Squared Error')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()

best_lag, best_mse
