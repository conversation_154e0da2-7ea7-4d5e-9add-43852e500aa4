package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"sort"
	"time"
)

// PriceData 结构体用于解析 JSON 中的价格和时间戳
type PriceData struct {
	Price float64 `json:"Price"`
	Ts    int64   `json:"Ts"`
}

func main() {
	// 定义时间戳和价格的容忍度
	// TsToleranceMillis 定义了两个时间戳被认为是“接近”的最大毫秒差
	const TsToleranceMillis int64 = 500 // 500 毫秒
	// PriceTolerance 定义了两个价格被认为是“接近”的最大差值
	const PriceTolerance float64 = 0.01 // 0.01 (例如，对于两位小数的价格，允许 0.01 的误差)

	// 读取第一个 JSON 文件
	file1Content, err := ioutil.ReadFile("price1.json")
	if err != nil {
		fmt.Printf("Error reading price1.json: %v\n", err)
		return
	}

	var prices1 []PriceData
	err = json.Unmarshal(file1Content, &prices1)
	if err != nil {
		fmt.Printf("Error unmarshaling price1.json: %v\n", err)
		return
	}

	// 读取第二个 JSON 文件
	file2Content, err := ioutil.ReadFile("price2.json")
	if err != nil {
		fmt.Printf("Error reading price2.json: %v\n", err)
		return
	}

	var prices2 []PriceData
	err = json.Unmarshal(file2Content, &prices2)
	if err != nil {
		fmt.Printf("Error unmarshaling price2.json: %v\n", err)
		return
	}

	// 将时间戳和价格存入 map 以便快速查找，并保留原始数据用于后续比较
	// key: timestamp, value: price
	timestamps1Map := make(map[int64]float64)
	for _, data := range prices1 {
		timestamps1Map[data.Ts] = data.Price
	}

	timestamps2Map := make(map[int64]float64)
	for _, data := range prices2 {
		timestamps2Map[data.Ts] = data.Price
	}

	// // --- 1. 查找仅存在于一个数组中的精确时间戳 ---
	// fmt.Println("--- Exact Timestamps Present in price1.json but not in price2.json: ---")
	// for ts, price := range timestamps1Map {
	// 	if _, found := timestamps2Map[ts]; !found {
	// 		fmt.Printf("  TriggerTimestamp: %d (%s), Price: %.2f\n", ts, convertUnixMillisToTime(ts), price)
	// 	}
	// }

	// fmt.Println("\n--- Exact Timestamps Present in price2.json but not in price1.json: ---")
	// for ts, price := range timestamps2Map {
	// 	if _, found := timestamps1Map[ts]; !found {
	// 		fmt.Printf("  TriggerTimestamp: %d (%s), Price: %.2f\n", ts, convertUnixMillisToTime(ts), price)
	// 	}
	// }

	// // --- 2. 查找相同时间戳但价格不同的情况 (精确时间戳匹配) ---
	// fmt.Println("\n--- Exact Timestamps with Differing Prices: ---")
	// for ts1, price1 := range timestamps1Map {
	// 	if price2, found := timestamps2Map[ts1]; found {
	// 		if price1 != price2 {
	// 			fmt.Printf("  TriggerTimestamp: %d (%s), Price1: %.2f, Price2: %.2f, Price Diff: %.2f\n",
	// 				ts1, convertUnixMillisToTime(ts1), price1, price2, math.Abs(price1-price2))
	// 		}
	// 	}
	// }

	// // --- 3. 查找“接近”的时间戳和价格差异 ---
	// // 遍历 prices1，尝试在 prices2 中找到最接近的匹配
	// fmt.Println("\n--- Closest Matches and Differences (Price1 vs Price2): ---")
	// foundMatchesIn2 := make(map[int]bool) // 记录 prices2 中哪些索引已被匹配，避免重复匹配

	// for i, data1 := range prices1 {
	// 	closestMatchIndex := -1
	// 	minTsDiff := TsToleranceMillis + 1 // 初始值设为超出容忍度
	// 	minPriceDiff := PriceTolerance + 1 // 初始值设为超出容忍度

	// 	for j, data2 := range prices2 {
	// 		if foundMatchesIn2[j] { // 如果这个 prices2 的元素已经被匹配过，则跳过
	// 			continue
	// 		}

	// 		tsDiff := absInt64(data1.Ts - data2.Ts)
	// 		priceDiff := math.Abs(data1.Price - data2.Price)

	// 		// 如果时间戳和价格都在容忍度内
	// 		if tsDiff <= TsToleranceMillis && priceDiff <= PriceTolerance {
	// 			// 找到一个更接近的匹配 (优先考虑时间戳差异更小的)
	// 			if tsDiff < minTsDiff || (tsDiff == minTsDiff && priceDiff < minPriceDiff) {
	// 				minTsDiff = tsDiff
	// 				minPriceDiff = priceDiff
	// 				closestMatchIndex = j
	// 			}
	// 		}
	// 	}

	// 	if closestMatchIndex != -1 {
	// 		data2 := prices2[closestMatchIndex]
	// 		foundMatchesIn2[closestMatchIndex] = true // 标记为已匹配

	// 		tsDiff := absInt64(data1.Ts - data2.Ts)
	// 		priceDiff := math.Abs(data1.Price - data2.Price)

	// 		fmt.Printf("  Price1 Entry %d (Ts: %s, Price: %.2f) matches Price2 Entry %d (Ts: %d, Price: %.2f)\n",
	// 			i, convertUnixMillisToTime(data1.Ts), data1.Price, closestMatchIndex, data2.Ts, data2.Price)
	// 		if tsDiff > 0 {
	// 			fmt.Printf("    TriggerTimestamp Difference: %d ms\n", tsDiff)
	// 		}
	// 		if priceDiff > 0 {
	// 			fmt.Printf("    Price Difference: %.2f\n", priceDiff)
	// 		}
	// 	} else {
	// 		fmt.Printf("  Price1 Entry %d (Ts: %s, Price: %.2f) has no close match in Price2.\n",
	// 			i, convertUnixMillisToTime(data1.Ts), data1.Price)
	// 	}
	// }

	// // 查找 prices2 中未被匹配的元素
	// fmt.Println("\n--- Unmatched Entries in Price2: ---")
	// for j, data2 := range prices2 {
	// 	if !foundMatchesIn2[j] {
	// 		fmt.Printf("  Price2 Entry %d (Ts: %s, Price: %.2f) has no close match in Price1.\n",
	// 			j, convertUnixMillisToTime(data2.Ts), data2.Price)
	// 	}
	// }

	// // --- 4. 尾部时间戳差异分析 (如您最初的需求) ---
	fmt.Println("\n--- Analyzing TriggerTimestamp Differences at the End of the Arrays: ---")
	// 确定较短数组的长度
	minLength := len(prices1)
	if len(prices2) < minLength {
		minLength = len(prices2)
	}

	// 比较最后 N 个元素 (这里我们选择最后 5 个作为示例，您可以调整)
	compareLastN := 5
	if minLength < compareLastN {
		compareLastN = minLength // 如果数组长度小于N，则比较所有元素
	}

	if compareLastN > 0 {
		// 获取两个数组的最后 N 个元素
		tail1 := prices1[len(prices1)-compareLastN:]
		tail2 := prices2[len(prices2)-compareLastN:]

		// 为了更好地比较，我们可以按时间戳对尾部数据进行排序 (如果它们不是按时间戳严格排序的)
		sort.Slice(
			tail1, func(i, j int) bool {
				return tail1[i].Ts < tail1[j].Ts
			},
		)
		sort.Slice(
			tail2, func(i, j int) bool {
				return tail2[i].Ts < tail2[j].Ts
			},
		)

		for i := 0; i < compareLastN; i++ {
			// 确保索引不越界
			if i < len(tail1) && i < len(tail2) {
				tsDiff := tail1[i].Ts - tail2[i].Ts
				fmt.Printf(
					"  Last element %d: price1.Ts: %d (%s), price2.Ts: %d (%s), Ts difference: %d ms\n",
					i+1, tail1[i].Ts, convertUnixMillisToTime(tail1[i].Ts), tail2[i].Ts,
					convertUnixMillisToTime(tail2[i].Ts), tsDiff,
				)
			} else {
				// 如果一个数组在尾部比另一个短，报告剩余的元素
				if i < len(tail1) {
					fmt.Printf(
						"  Price1 has extra element %d at the end: Ts: %d (%s), Price: %.2f\n",
						i+1, tail1[i].Ts, convertUnixMillisToTime(tail1[i].Ts), tail1[i].Price,
					)
				}
				if i < len(tail2) {
					fmt.Printf(
						"  Price2 has extra element %d at the end: Ts: %d (%s), Price: %.2f\n",
						i+1, tail2[i].Ts, convertUnixMillisToTime(tail2[i].Ts), tail2[i].Price,
					)
				}
			}
		}
	} else {
		fmt.Println("  Not enough elements to compare at the end of the arrays.")
	}
}

// convertUnixMillisToTime 将 Unix 毫秒时间戳转换为可读的日期时间字符串
func convertUnixMillisToTime(ms int64) string {
	t := time.Unix(0, ms*int64(time.Millisecond))
	return t.Format("2006-01-02 15:04:05.000")
}

// absInt64 返回 int64 值的绝对值
func absInt64(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}
