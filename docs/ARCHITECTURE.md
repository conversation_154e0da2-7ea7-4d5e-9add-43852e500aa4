# 套利系统架构文档

## 概述

本套利系统采用了全新的分层架构设计，遵循领域驱动设计（DDD）和清洁架构原则，具有高度的可扩展性和可维护性。

## 架构层次

### 1. 核心层 (Core Layer)
位置: `internal/core/`

定义系统的核心抽象和业务规则，不依赖任何外部框架或技术实现。

**主要组件:**
- `Exchange`: 交易所接口
- `PriceStream`: 价格数据流接口
- `PriceAnalyzer`: 价格分析器接口
- `EventBus`: 事件总线接口
- `Connection`: WebSocket连接接口

**核心实体:**
- `PriceData`: 价格数据
- `ArbitrageOpportunity`: 套利机会
- `CorrelationResult`: 相关性分析结果
- `Event`: 系统事件

### 2. 基础设施层 (Infrastructure Layer)
位置: `internal/infrastructure/`

实现核心层定义的接口，处理与外部系统的交互。

**子模块:**

#### WebSocket模块 (`websocket/`)
- `connection.go`: WebSocket连接的具体实现
- 支持自动重连、心跳检测、错误处理

#### 交易所模块 (`exchange/`)
- `manager.go`: 交易所管理器实现
- `mexc.go`: MEXC交易所实现
- `lbank.go`: LBank交易所实现

#### 分析器模块 (`analyzer/`)
- `price_analyzer.go`: 价格分析器实现
- 支持相关性分析和套利机会检测

#### 事件模块 (`event/`)
- `bus.go`: 事件总线实现
- 支持异步事件发布和订阅

### 3. 应用层 (Application Layer)
位置: `internal/application/`

协调各个组件，实现业务用例。

**主要组件:**
- `ArbitrageService`: 套利服务，整合所有功能模块

### 4. 接口层 (Interface Layer)
位置: `cmd/`

提供系统的入口点和用户界面。

**组件:**
- `cmd/arbitrage/main.go`: 主程序入口

## 核心特性

### 1. 事件驱动架构
系统采用事件驱动架构，通过事件总线实现组件间的解耦：

```go
// 事件类型
const (
    EventTypePriceUpdate     EventType = "price_update"
    EventTypeArbitrage       EventType = "arbitrage"
    EventTypeConnectionError EventType = "connection_error"
    EventTypeCorrelation     EventType = "correlation"
)
```

### 2. 可扩展的交易所支持
通过接口抽象，可以轻松添加新的交易所：

```go
type Exchange interface {
    Name() string
    Connect(ctx context.Context) error
    Disconnect() error
    IsConnected() bool
    GetPriceStream() PriceStream
    Health() error
}
```

### 3. 实时价格监控
每个交易所都有独立的价格数据流：

```go
type PriceStream interface {
    Subscribe(ctx context.Context, symbol string) (<-chan PriceData, error)
    Unsubscribe(symbol string) error
    Close() error
}
```

### 4. 智能分析引擎
支持多种分析功能：
- 价格相关性分析
- 套利机会检测
- 时间延迟计算

## 数据流

```
WebSocket连接 -> 价格数据流 -> 价格缓存 -> 分析引擎 -> 事件发布 -> 用户界面
```

1. **数据采集**: WebSocket连接从各交易所实时获取价格数据
2. **数据处理**: 价格数据经过格式化和验证
3. **数据缓存**: 最新价格数据存储在内存缓存中
4. **数据分析**: 分析引擎定期分析价格数据，检测套利机会和相关性
5. **事件发布**: 分析结果通过事件总线发布
6. **用户通知**: 用户界面接收事件并显示相关信息

## 配置和扩展

### 添加新交易所

1. 实现 `Exchange` 接口
2. 实现 `PriceStream` 接口
3. 在 `ArbitrageService` 中注册新交易所

```go
// 示例：添加新交易所
func NewCustomExchange() core.Exchange {
    // 实现交易所逻辑
}

// 在服务中注册
customExchange := exchange.NewCustomExchange()
service.exchangeManager.RegisterExchange(customExchange)
```

### 自定义分析器

实现 `PriceAnalyzer` 接口来添加自定义分析逻辑：

```go
type CustomAnalyzer struct {
    // 自定义字段
}

func (ca *CustomAnalyzer) AnalyzeCorrelation(ctx context.Context, data1, data2 []core.PriceData) (core.CorrelationResult, error) {
    // 自定义相关性分析逻辑
}

func (ca *CustomAnalyzer) DetectArbitrageOpportunity(ctx context.Context, prices map[string]core.PriceData) ([]core.ArbitrageOpportunity, error) {
    // 自定义套利检测逻辑
}
```

## 运行系统

```bash
# 编译
go build -o arbitrage cmd/arbitrage/main.go

# 运行
./arbitrage
```

## 监控和日志

系统通过事件总线提供实时监控：
- 价格更新事件
- 套利机会事件
- 相关性分析事件
- 连接错误事件

所有事件都包含时间戳和来源信息，便于调试和监控。

## 性能优化

1. **并发处理**: 每个交易所使用独立的goroutine处理数据
2. **非阻塞通道**: 使用带缓冲的通道避免阻塞
3. **内存管理**: 定期清理过期的历史数据
4. **连接复用**: WebSocket连接支持自动重连和心跳检测

## 错误处理

系统采用多层错误处理策略：
1. **连接层**: 自动重连和错误恢复
2. **应用层**: 错误日志记录和事件发布
3. **用户层**: 友好的错误提示和状态显示

这种架构设计确保了系统的高可用性、可扩展性和可维护性。